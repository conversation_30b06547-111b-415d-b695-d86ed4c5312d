import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { InvoiceProvider } from './context/InvoiceContext';
import { Layout } from './components/Layout';
import { InvoiceCreator } from './components/InvoiceCreator';
import { TemplatePage } from './components/TemplatePage';

function App() {
  return (
    <InvoiceProvider>
      <Router>
        <Layout>
          <Routes>
            <Route path="/" element={<InvoiceCreator />} />
            <Route path="/template" element={<TemplatePage />} />
          </Routes>
        </Layout>
      </Router>
    </InvoiceProvider>
  );
}

export default App;