export interface InvoiceItem {
  id: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
}

export interface CompanyInfo {
  name: string;
  logo?: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  phone: string;
  email: string;
  cnpj?: string;
  website?: string;
}

export interface ClientInfo {
  name: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  phone: string;
  email: string;
  cpfCnpj?: string;
}

export interface ShippingInfo {
  address?: string;
  city?: string;
  state?: string;
  zipCode?: string;
  method?: string;
  cost?: number;
}

export interface PixInfo {
  key: string;
  name: string;
  city: string;
  amount: number;
}

export interface InvoiceDetails {
  number: string;
  issueDate: Date;
  dueDate: Date;
  currency: string;
  notes?: string;
  termsConditions?: string;
}

export interface InvoiceData {
  details: InvoiceDetails;
  company: CompanyInfo;
  client: ClientInfo;
  shipping: ShippingInfo;
  items: InvoiceItem[];
  pix: PixInfo;
  subtotal: number;
  taxRate: number;
  taxAmount: number;
  total: number;
  template: string;
}

export type InvoiceStep = 'details' | 'parties' | 'shipping' | 'items' | 'preview';