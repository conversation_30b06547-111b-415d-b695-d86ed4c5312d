import React, { useState } from 'react';
import { Eye, FileText, QrCode, DollarSign, Calculator } from 'lucide-react';
import { useInvoice } from '../../context/InvoiceContext';
import { formatCurrency, formatDate } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

const templates = [
  { id: 'modern', name: 'Moderno', description: 'Design limpo e contemporâneo' },
  { id: 'professional', name: 'Profissional', description: 'Layout corporativo formal' },
  { id: 'minimal', name: 'Minimalista', description: 'Design simples e elegante' },
  { id: 'colorful', name: 'Colorido', description: 'Visual vibrante e alegre' },
  { id: 'classic', name: 'Clássico', description: 'Estilo tradicional e confiável' },
  { id: 'creative', name: 'Criativo', description: 'Design diferenciado e artístico' },
  { id: 'corporate', name: 'Corporativo', description: 'Formal para grandes empresas' },
  { id: 'startup', name: 'Startup', description: 'Jovem e inovador' },
  { id: 'elegant', name: 'Elegante', description: 'Sofisticado e refinado' },
  { id: 'bold', name: 'Ousado', description: 'Impactante e marcante' },
];

export function PreviewStep() {
  const { state, dispatch } = useInvoice();
  const { data } = state;
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  React.useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  const handleTemplateSelect = (templateId: string) => {
    dispatch({ type: 'UPDATE_TEMPLATE', payload: templateId });
  };

  const updatePixData = (field: keyof typeof data.pix, value: string | number) => {
    dispatch({
      type: 'UPDATE_PIX',
      payload: { [field]: value },
    });
  };

  return (
    <div className="space-y-8">
      {/* Template Selection */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <FileText className="w-5 h-5 mr-3 text-purple-600" />
          Escolha o Template
        </h2>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
          {templates.map((template) => (
            <div
              key={template.id}
              onClick={() => handleTemplateSelect(template.id)}
              className={`cursor-pointer p-4 rounded-lg border-2 transition-all duration-300 hover:scale-105 ${
                data.template === template.id
                  ? 'border-purple-500 bg-purple-50'
                  : 'border-gray-200 hover:border-purple-300'
              }`}
            >
              <div className={`w-full h-20 rounded-lg mb-3 flex items-center justify-center ${
                data.template === template.id ? 'bg-purple-100' : 'bg-gray-100'
              }`}>
                <FileText className={`w-8 h-8 ${
                  data.template === template.id ? 'text-purple-600' : 'text-gray-400'
                }`} />
              </div>
              <h3 className="font-medium text-sm text-gray-900">{template.name}</h3>
              <p className="text-xs text-gray-500 mt-1">{template.description}</p>
            </div>
          ))}
        </div>
      </div>

      {/* PIX Configuration */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <QrCode className="w-5 h-5 mr-3 text-green-600" />
          Configuração PIX
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Chave PIX *
              </label>
              <input
                type="text"
                value={data.pix.key}
                onChange={(e) => updatePixData('key', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="CPF, CNPJ, email, telefone ou chave aleatória"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome do Recebedor *
              </label>
              <input
                type="text"
                value={data.pix.name}
                onChange={(e) => updatePixData('name', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="Nome para o PIX"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cidade do Recebedor *
              </label>
              <input
                type="text"
                value={data.pix.city}
                onChange={(e) => updatePixData('city', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="Cidade para o PIX"
              />
            </div>
          </div>

          <div className="flex flex-col items-center justify-center space-y-4">
            {qrCodeUrl ? (
              <div className="text-center">
                <img 
                  src={qrCodeUrl} 
                  alt="QR Code PIX" 
                  className="w-40 h-40 border border-gray-300 rounded-lg"
                />
                <p className="text-sm text-gray-600 mt-2">
                  Valor: {formatCurrency(data.total)}
                </p>
              </div>
            ) : (
              <div className="w-40 h-40 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                <div className="text-center text-gray-400">
                  <QrCode className="w-8 h-8 mx-auto mb-2" />
                  <p className="text-sm">Preencha os dados PIX</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Invoice Summary */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <Calculator className="w-5 h-5 mr-3 text-blue-600" />
          Resumo da Fatura
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Detalhes</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Número:</span>
                  <span className="font-medium">{data.details.number}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Emissão:</span>
                  <span className="font-medium">{formatDate(data.details.issueDate)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Vencimento:</span>
                  <span className="font-medium">{formatDate(data.details.dueDate)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Template:</span>
                  <span className="font-medium">{templates.find(t => t.id === data.template)?.name}</span>
                </div>
              </div>
            </div>

            <div>
              <h3 className="font-medium text-gray-900 mb-2">Partes</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Empresa:</span>
                  <span className="font-medium">{data.company.name || 'Não informado'}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Cliente:</span>
                  <span className="font-medium">{data.client.name || 'Não informado'}</span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="font-medium text-gray-900 mb-2">Itens</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Quantidade de itens:</span>
                  <span className="font-medium">{data.items.length}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="font-medium">{formatCurrency(data.subtotal)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Impostos ({data.taxRate}%):</span>
                  <span className="font-medium">{formatCurrency(data.taxAmount)}</span>
                </div>
                <div className="flex justify-between pt-2 border-t border-gray-200">
                  <span className="text-gray-900 font-semibold">Total:</span>
                  <span className="font-bold text-purple-600 text-lg">{formatCurrency(data.total)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}