import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Truck, MapPin, DollarSign } from 'lucide-react';
import { useInvoice } from '../../context/InvoiceContext';
import { formatCEP } from '../../utils/invoice';

const shippingSchema = z.object({
  address: z.string().optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  zipCode: z.string().optional(),
  method: z.string().optional(),
  cost: z.number().min(0, 'Custo deve ser positivo').optional(),
});

type ShippingFormData = z.infer<typeof shippingSchema>;

export function ShippingStep() {
  const { state, dispatch } = useInvoice();
  const { shipping } = state.data;

  const { register, handleSubmit, formState: { errors }, watch, setValue } = useForm<ShippingFormData>({
    resolver: zodResolver(shippingSchema),
    defaultValues: {
      address: shipping.address || '',
      city: shipping.city || '',
      state: shipping.state || '',
      zipCode: shipping.zipCode || '',
      method: shipping.method || '',
      cost: shipping.cost || 0,
    },
  });

  React.useEffect(() => {
    const subscription = watch((value) => {
      dispatch({
        type: 'UPDATE_SHIPPING',
        payload: {
          address: value.address || '',
          city: value.city || '',
          state: value.state || '',
          zipCode: value.zipCode || '',
          method: value.method || '',
          cost: Number(value.cost) || 0,
        },
      });
    });
    return () => subscription.unsubscribe();
  }, [watch, dispatch]);

  const handleCEPChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const formatted = formatCEP(event.target.value);
    setValue('zipCode', formatted);
  };

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <Truck className="w-5 h-5 mr-3 text-green-600" />
          Informações de Entrega
        </h2>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <p className="text-blue-800 text-sm">
            <strong>Opcional:</strong> Preencha apenas se a entrega for diferente do endereço de cobrança do cliente.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="lg:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Endereço de Entrega
            </label>
            <input
              {...register('address')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Rua, número, complemento (se diferente do endereço do cliente)"
            />
            {errors.address && (
              <p className="text-red-500 text-sm mt-1">{errors.address.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cidade
            </label>
            <input
              {...register('city')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Cidade de entrega"
            />
            {errors.city && (
              <p className="text-red-500 text-sm mt-1">{errors.city.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estado
            </label>
            <input
              {...register('state')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Ex: SP, RJ, MG"
            />
            {errors.state && (
              <p className="text-red-500 text-sm mt-1">{errors.state.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CEP
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register('zipCode')}
                type="text"
                onChange={handleCEPChange}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="00000-000"
              />
            </div>
            {errors.zipCode && (
              <p className="text-red-500 text-sm mt-1">{errors.zipCode.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Método de Entrega
            </label>
            <select
              {...register('method')}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
            >
              <option value="">Selecione um método</option>
              <option value="correios">Correios</option>
              <option value="transportadora">Transportadora</option>
              <option value="entrega-propria">Entrega Própria</option>
              <option value="retirada">Retirada no Local</option>
              <option value="digital">Produto Digital</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Custo da Entrega
            </label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register('cost', { valueAsNumber: true })}
                type="number"
                step="0.01"
                min="0"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="0,00"
              />
            </div>
            {errors.cost && (
              <p className="text-red-500 text-sm mt-1">{errors.cost.message}</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}