import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Building2, Users, Phone, Mail, MapPin, Hash } from 'lucide-react';
import { useInvoice } from '../../context/InvoiceContext';
import { formatCPFCNPJ, formatPhone, formatCEP, validateCPFCNPJ } from '../../utils/invoice';

const partiesSchema = z.object({
  // Company data
  companyName: z.string().min(1, 'Nome da empresa é obrigatório'),
  companyAddress: z.string().min(1, 'Endereço da empresa é obrigatório'),
  companyCity: z.string().min(1, 'Cidade da empresa é obrigatória'),
  companyState: z.string().min(1, 'Estado da empresa é obrigatório'),
  companyZipCode: z.string().min(8, 'CEP da empresa é obrigatório'),
  companyPhone: z.string().min(10, 'Telefone da empresa é obrigatório'),
  companyEmail: z.string().email('Email da empresa deve ser válido'),
  companyCnpj: z.string().optional(),
  companyWebsite: z.string().optional(),
  
  // Client data
  clientName: z.string().min(1, 'Nome do cliente é obrigatório'),
  clientAddress: z.string().min(1, 'Endereço do cliente é obrigatório'),
  clientCity: z.string().min(1, 'Cidade do cliente é obrigatória'),
  clientState: z.string().min(1, 'Estado do cliente é obrigatório'),
  clientZipCode: z.string().min(8, 'CEP do cliente é obrigatório'),
  clientPhone: z.string().min(10, 'Telefone do cliente é obrigatório'),
  clientEmail: z.string().email('Email do cliente deve ser válido'),
  clientCpfCnpj: z.string().optional(),
});

type PartiesFormData = z.infer<typeof partiesSchema>;

export function PartiesStep() {
  const { state, dispatch } = useInvoice();
  const { company, client } = state.data;

  const { register, handleSubmit, formState: { errors }, watch, setValue } = useForm<PartiesFormData>({
    resolver: zodResolver(partiesSchema),
    defaultValues: {
      companyName: company.name,
      companyAddress: company.address,
      companyCity: company.city,
      companyState: company.state,
      companyZipCode: company.zipCode,
      companyPhone: company.phone,
      companyEmail: company.email,
      companyCnpj: company.cnpj,
      companyWebsite: company.website,
      
      clientName: client.name,
      clientAddress: client.address,
      clientCity: client.city,
      clientState: client.state,
      clientZipCode: client.zipCode,
      clientPhone: client.phone,
      clientEmail: client.email,
      clientCpfCnpj: client.cpfCnpj,
    },
  });

  React.useEffect(() => {
    const subscription = watch((value) => {
      dispatch({
        type: 'UPDATE_COMPANY',
        payload: {
          name: value.companyName || '',
          address: value.companyAddress || '',
          city: value.companyCity || '',
          state: value.companyState || '',
          zipCode: value.companyZipCode || '',
          phone: value.companyPhone || '',
          email: value.companyEmail || '',
          cnpj: value.companyCnpj || '',
          website: value.companyWebsite || '',
        },
      });

      dispatch({
        type: 'UPDATE_CLIENT',
        payload: {
          name: value.clientName || '',
          address: value.clientAddress || '',
          city: value.clientCity || '',
          state: value.clientState || '',
          zipCode: value.clientZipCode || '',
          phone: value.clientPhone || '',
          email: value.clientEmail || '',
          cpfCnpj: value.clientCpfCnpj || '',
        },
      });
    });
    return () => subscription.unsubscribe();
  }, [watch, dispatch]);

  const handleCPFCNPJChange = (field: 'companyCnpj' | 'clientCpfCnpj') => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const formatted = formatCPFCNPJ(event.target.value);
    setValue(field, formatted);
  };

  const handlePhoneChange = (field: 'companyPhone' | 'clientPhone') => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const formatted = formatPhone(event.target.value);
    setValue(field, formatted);
  };

  const handleCEPChange = (field: 'companyZipCode' | 'clientZipCode') => (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const formatted = formatCEP(event.target.value);
    setValue(field, formatted);
  };

  return (
    <div className="space-y-8">
      {/* Company Information */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <Building2 className="w-5 h-5 mr-3 text-purple-600" />
          Dados da Empresa
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="lg:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome da Empresa *
            </label>
            <input
              {...register('companyName')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Nome da sua empresa"
            />
            {errors.companyName && (
              <p className="text-red-500 text-sm mt-1">{errors.companyName.message}</p>
            )}
          </div>

          <div className="lg:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Endereço *
            </label>
            <input
              {...register('companyAddress')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Rua, número, complemento"
            />
            {errors.companyAddress && (
              <p className="text-red-500 text-sm mt-1">{errors.companyAddress.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cidade *
            </label>
            <input
              {...register('companyCity')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Cidade"
            />
            {errors.companyCity && (
              <p className="text-red-500 text-sm mt-1">{errors.companyCity.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estado *
            </label>
            <input
              {...register('companyState')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Ex: SP, RJ, MG"
            />
            {errors.companyState && (
              <p className="text-red-500 text-sm mt-1">{errors.companyState.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CEP *
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register('companyZipCode')}
                type="text"
                onChange={handleCEPChange('companyZipCode')}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="00000-000"
              />
            </div>
            {errors.companyZipCode && (
              <p className="text-red-500 text-sm mt-1">{errors.companyZipCode.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Telefone *
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register('companyPhone')}
                type="tel"
                onChange={handlePhoneChange('companyPhone')}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="(11) 99999-9999"
              />
            </div>
            {errors.companyPhone && (
              <p className="text-red-500 text-sm mt-1">{errors.companyPhone.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email *
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register('companyEmail')}
                type="email"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="<EMAIL>"
              />
            </div>
            {errors.companyEmail && (
              <p className="text-red-500 text-sm mt-1">{errors.companyEmail.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CNPJ
            </label>
            <div className="relative">
              <Hash className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register('companyCnpj')}
                type="text"
                onChange={handleCPFCNPJChange('companyCnpj')}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="00.000.000/0000-00"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Website
            </label>
            <input
              {...register('companyWebsite')}
              type="url"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="https://www.empresa.com"
            />
          </div>
        </div>
      </div>

      {/* Client Information */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <Users className="w-5 h-5 mr-3 text-blue-600" />
          Dados do Cliente
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="lg:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nome do Cliente *
            </label>
            <input
              {...register('clientName')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Nome do cliente ou empresa"
            />
            {errors.clientName && (
              <p className="text-red-500 text-sm mt-1">{errors.clientName.message}</p>
            )}
          </div>

          <div className="lg:col-span-2">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Endereço *
            </label>
            <input
              {...register('clientAddress')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Rua, número, complemento"
            />
            {errors.clientAddress && (
              <p className="text-red-500 text-sm mt-1">{errors.clientAddress.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Cidade *
            </label>
            <input
              {...register('clientCity')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Cidade"
            />
            {errors.clientCity && (
              <p className="text-red-500 text-sm mt-1">{errors.clientCity.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Estado *
            </label>
            <input
              {...register('clientState')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Ex: SP, RJ, MG"
            />
            {errors.clientState && (
              <p className="text-red-500 text-sm mt-1">{errors.clientState.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CEP *
            </label>
            <div className="relative">
              <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register('clientZipCode')}
                type="text"
                onChange={handleCEPChange('clientZipCode')}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="00000-000"
              />
            </div>
            {errors.clientZipCode && (
              <p className="text-red-500 text-sm mt-1">{errors.clientZipCode.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Telefone *
            </label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register('clientPhone')}
                type="tel"
                onChange={handlePhoneChange('clientPhone')}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="(11) 99999-9999"
              />
            </div>
            {errors.clientPhone && (
              <p className="text-red-500 text-sm mt-1">{errors.clientPhone.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email *
            </label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register('clientEmail')}
                type="email"
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="<EMAIL>"
              />
            </div>
            {errors.clientEmail && (
              <p className="text-red-500 text-sm mt-1">{errors.clientEmail.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CPF/CNPJ
            </label>
            <div className="relative">
              <Hash className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                {...register('clientCpfCnpj')}
                type="text"
                onChange={handleCPFCNPJChange('clientCpfCnpj')}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="000.000.000-00 ou 00.000.000/0000-00"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}