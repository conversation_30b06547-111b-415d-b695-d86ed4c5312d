import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Calendar, Hash, DollarSign, Upload, X } from 'lucide-react';
import { useInvoice } from '../../context/InvoiceContext';
import { formatDate } from '../../utils/invoice';

const detailsSchema = z.object({
  number: z.string().min(1, 'Número da fatura é obrigatório'),
  issueDate: z.string().min(1, 'Data de emissão é obrigatória'),
  dueDate: z.string().min(1, 'Data de vencimento é obrigatória'),
  currency: z.string().min(1, 'Moeda é obrigatória'),
  notes: z.string().optional(),
  termsConditions: z.string().optional(),
});

type DetailsFormData = z.infer<typeof detailsSchema>;

export function DetailsStep() {
  const { state, dispatch } = useInvoice();
  const { details, company } = state.data;

  const { register, handleSubmit, formState: { errors }, watch } = useForm<DetailsFormData>({
    resolver: zodResolver(detailsSchema),
    defaultValues: {
      number: details.number,
      issueDate: details.issueDate.toISOString().split('T')[0],
      dueDate: details.dueDate.toISOString().split('T')[0],
      currency: details.currency,
      notes: details.notes || '',
      termsConditions: details.termsConditions || '',
    },
  });

  const watchedValues = watch();

  React.useEffect(() => {
    const subscription = watch((value) => {
      if (value.issueDate && value.dueDate && value.number && value.currency) {
        dispatch({
          type: 'UPDATE_DETAILS',
          payload: {
            number: value.number,
            issueDate: new Date(value.issueDate),
            dueDate: new Date(value.dueDate),
            currency: value.currency,
            notes: value.notes,
            termsConditions: value.termsConditions,
          },
        });
      }
    });
    return () => subscription.unsubscribe();
  }, [watch, dispatch]);

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        dispatch({
          type: 'UPDATE_COMPANY',
          payload: { logo: result },
        });
      };
      reader.readAsDataURL(file);
    }
  };

  const removeLogo = () => {
    dispatch({
      type: 'UPDATE_COMPANY',
      payload: { logo: undefined },
    });
  };

  return (
    <div className="space-y-8">
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center">
          <Hash className="w-6 h-6 mr-3 text-purple-600" />
          Detalhes da Fatura
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Número da Fatura *
              </label>
              <div className="relative">
                <Hash className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <input
                  {...register('number')}
                  type="text"
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                  placeholder="Ex: INV2024001"
                />
              </div>
              {errors.number && (
                <p className="text-red-500 text-sm mt-1">{errors.number.message}</p>
              )}
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Data de Emissão *
                </label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <input
                    {...register('issueDate')}
                    type="date"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                  />
                </div>
                {errors.issueDate && (
                  <p className="text-red-500 text-sm mt-1">{errors.issueDate.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Data de Vencimento *
                </label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <input
                    {...register('dueDate')}
                    type="date"
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                  />
                </div>
                {errors.dueDate && (
                  <p className="text-red-500 text-sm mt-1">{errors.dueDate.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Moeda *
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <select
                  {...register('currency')}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                >
                  <option value="BRL">Real Brasileiro (R$)</option>
                  <option value="USD">Dólar Americano (US$)</option>
                  <option value="EUR">Euro (€)</option>
                </select>
              </div>
              {errors.currency && (
                <p className="text-red-500 text-sm mt-1">{errors.currency.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Logo da Empresa
              </label>
              {company.logo ? (
                <div className="relative inline-block">
                  <img 
                    src={company.logo} 
                    alt="Logo da empresa" 
                    className="w-32 h-32 object-contain border border-gray-300 rounded-lg bg-gray-50"
                  />
                  <button
                    onClick={removeLogo}
                    className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors"
                  >
                    <X className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100 transition-colors">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <Upload className="w-8 h-8 mb-2 text-gray-400" />
                    <p className="text-sm text-gray-500">Clique para enviar logo</p>
                    <p className="text-xs text-gray-400">PNG, JPG até 2MB</p>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    accept="image/*"
                    onChange={handleLogoUpload}
                  />
                </label>
              )}
            </div>
          </div>
        </div>

        <div className="mt-8 space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Observações
            </label>
            <textarea
              {...register('notes')}
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors resize-none"
              placeholder="Observações adicionais sobre a fatura..."
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Termos e Condições
            </label>
            <textarea
              {...register('termsConditions')}
              rows={4}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors resize-none"
              placeholder="Termos e condições de pagamento e entrega..."
            />
          </div>
        </div>
      </div>
    </div>
  );
}