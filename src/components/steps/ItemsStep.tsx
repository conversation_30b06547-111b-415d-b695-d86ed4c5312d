import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Plus, Trash2, Package, Calculator, Percent } from 'lucide-react';
import { useInvoice } from '../../context/InvoiceContext';
import { InvoiceItem } from '../../types/invoice';
import { formatCurrency, formatNumber } from '../../utils/invoice';

const itemSchema = z.object({
  description: z.string().min(1, 'Descrição é obrigatória'),
  quantity: z.number().min(0.01, 'Quantidade deve ser maior que zero'),
  unitPrice: z.number().min(0.01, 'Preço unitário deve ser maior que zero'),
});

type ItemFormData = z.infer<typeof itemSchema>;

export function ItemsStep() {
  const { state, dispatch } = useInvoice();
  const { items, subtotal, taxRate, taxAmount, total } = state.data;
  const [editingItem, setEditingItem] = useState<string | null>(null);

  const { register, handleSubmit, formState: { errors }, reset, setValue, watch } = useForm<ItemFormData>({
    resolver: zodResolver(itemSchema),
    defaultValues: {
      description: '',
      quantity: 1,
      unitPrice: 0,
    },
  });

  const watchedValues = watch();
  const itemTotal = (watchedValues.quantity || 0) * (watchedValues.unitPrice || 0);

  const handleAddItem = (data: ItemFormData) => {
    const newItem: InvoiceItem = {
      id: Date.now().toString(),
      description: data.description,
      quantity: data.quantity,
      unitPrice: data.unitPrice,
      total: data.quantity * data.unitPrice,
    };

    const updatedItems = editingItem
      ? items.map(item => item.id === editingItem ? newItem : item)
      : [...items, newItem];

    dispatch({ type: 'UPDATE_ITEMS', payload: updatedItems });
    reset();
    setEditingItem(null);
  };

  const handleEditItem = (item: InvoiceItem) => {
    setEditingItem(item.id);
    setValue('description', item.description);
    setValue('quantity', item.quantity);
    setValue('unitPrice', item.unitPrice);
  };

  const handleDeleteItem = (id: string) => {
    const updatedItems = items.filter(item => item.id !== id);
    dispatch({ type: 'UPDATE_ITEMS', payload: updatedItems });
  };

  const handleTaxRateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const rate = parseFloat(event.target.value) || 0;
    dispatch({ type: 'UPDATE_DETAILS', payload: { taxRate: rate } });
    dispatch({ type: 'CALCULATE_TOTALS' });
  };

  return (
    <div className="space-y-8">
      {/* Add/Edit Item Form */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
          <Package className="w-5 h-5 mr-3 text-green-600" />
          {editingItem ? 'Editar Item' : 'Adicionar Item'}
        </h2>

        <form onSubmit={handleSubmit(handleAddItem)} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Descrição do Item *
            </label>
            <input
              {...register('description')}
              type="text"
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
              placeholder="Ex: Consultoria em desenvolvimento web"
            />
            {errors.description && (
              <p className="text-red-500 text-sm mt-1">{errors.description.message}</p>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Quantidade *
              </label>
              <input
                {...register('quantity', { valueAsNumber: true })}
                type="number"
                step="0.01"
                min="0.01"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="1"
              />
              {errors.quantity && (
                <p className="text-red-500 text-sm mt-1">{errors.quantity.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preço Unitário *
              </label>
              <input
                {...register('unitPrice', { valueAsNumber: true })}
                type="number"
                step="0.01"
                min="0.01"
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="0,00"
              />
              {errors.unitPrice && (
                <p className="text-red-500 text-sm mt-1">{errors.unitPrice.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Total do Item
              </label>
              <div className="w-full px-4 py-3 bg-gray-50 border border-gray-300 rounded-lg text-gray-900 font-semibold">
                {formatCurrency(itemTotal)}
              </div>
            </div>
          </div>

          <div className="flex space-x-4">
            <button
              type="submit"
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-6 py-3 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>{editingItem ? 'Atualizar Item' : 'Adicionar Item'}</span>
            </button>
            
            {editingItem && (
              <button
                type="button"
                onClick={() => {
                  setEditingItem(null);
                  reset();
                }}
                className="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
            )}
          </div>
        </form>
      </div>

      {/* Items List */}
      {items.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Lista de Itens</h3>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 text-sm font-medium text-gray-700">Descrição</th>
                  <th className="text-right py-3 px-4 text-sm font-medium text-gray-700">Qtd</th>
                  <th className="text-right py-3 px-4 text-sm font-medium text-gray-700">Preço Unit.</th>
                  <th className="text-right py-3 px-4 text-sm font-medium text-gray-700">Total</th>
                  <th className="text-center py-3 px-4 text-sm font-medium text-gray-700">Ações</th>
                </tr>
              </thead>
              <tbody>
                {items.map((item) => (
                  <tr key={item.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                    <td className="py-3 px-4 text-sm text-gray-900">{item.description}</td>
                    <td className="py-3 px-4 text-sm text-gray-900 text-right">{formatNumber(item.quantity)}</td>
                    <td className="py-3 px-4 text-sm text-gray-900 text-right">{formatCurrency(item.unitPrice)}</td>
                    <td className="py-3 px-4 text-sm text-gray-900 text-right font-medium">{formatCurrency(item.total)}</td>
                    <td className="py-3 px-4 text-center">
                      <div className="flex items-center justify-center space-x-2">
                        <button
                          onClick={() => handleEditItem(item)}
                          className="text-blue-600 hover:text-blue-800 transition-colors"
                        >
                          Editar
                        </button>
                        <button
                          onClick={() => handleDeleteItem(item.id)}
                          className="text-red-600 hover:text-red-800 transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Totals */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Calculator className="w-5 h-5 mr-3 text-purple-600" />
          Cálculos e Totais
        </h3>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Taxa de Impostos (%)
            </label>
            <div className="relative">
              <Percent className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                type="number"
                step="0.01"
                min="0"
                max="100"
                value={taxRate}
                onChange={handleTaxRateChange}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-colors"
                placeholder="0,00"
              />
            </div>
          </div>

          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Subtotal:</span>
              <span className="font-medium text-gray-900">{formatCurrency(subtotal)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Impostos ({taxRate}%):</span>
              <span className="font-medium text-gray-900">{formatCurrency(taxAmount)}</span>
            </div>
            <div className="flex justify-between items-center pt-3 border-t border-gray-300">
              <span className="text-lg font-semibold text-gray-900">Total:</span>
              <span className="text-lg font-bold text-purple-600">{formatCurrency(total)}</span>
            </div>
          </div>
        </div>

        {items.length === 0 && (
          <div className="text-center py-12 text-gray-500">
            <Package className="w-16 h-16 mx-auto mb-4 text-gray-300" />
            <h4 className="text-lg font-medium text-gray-900 mb-2">Nenhum item adicionado</h4>
            <p>Adicione pelo menos um item para continuar.</p>
          </div>
        )}
      </div>
    </div>
  );
}