import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function ModernTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-8 font-sans">
      {/* Header with gradient */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6 rounded-t-lg mb-8">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-4">
            {data.company.logo && (
              <img 
                src={data.company.logo} 
                alt="Logo" 
                className="w-16 h-16 object-contain bg-white rounded-lg p-2"
              />
            )}
            <div>
              <h1 className="text-2xl font-bold">{data.company.name}</h1>
              <p className="text-purple-100">{data.company.website}</p>
            </div>
          </div>
          <div className="text-right">
            <h2 className="text-2xl font-bold">FATURA</h2>
            <p className="text-lg font-semibold">#{data.details.number}</p>
          </div>
        </div>
      </div>

      {/* Company and Client Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div className="bg-gray-50 p-4 rounded-lg">
          <h3 className="font-bold text-purple-600 mb-3 text-lg">DE:</h3>
          <div className="space-y-1 text-sm">
            <p className="font-semibold">{data.company.name}</p>
            <p>{data.company.address}</p>
            <p>{data.company.city}, {data.company.state} {data.company.zipCode}</p>
            <p>Tel: {data.company.phone}</p>
            <p>Email: {data.company.email}</p>
            {data.company.cnpj && <p>CNPJ: {data.company.cnpj}</p>}
          </div>
        </div>
        
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="font-bold text-blue-600 mb-3 text-lg">PARA:</h3>
          <div className="space-y-1 text-sm">
            <p className="font-semibold">{data.client.name}</p>
            <p>{data.client.address}</p>
            <p>{data.client.city}, {data.client.state} {data.client.zipCode}</p>
            <p>Tel: {data.client.phone}</p>
            <p>Email: {data.client.email}</p>
            {data.client.cpfCnpj && <p>CPF/CNPJ: {data.client.cpfCnpj}</p>}
          </div>
        </div>
      </div>

      {/* Invoice Details */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="text-center bg-purple-50 p-4 rounded-lg">
          <h4 className="font-semibold text-purple-600 mb-1">Data de Emissão</h4>
          <p className="text-gray-900 font-medium">{formatDate(data.details.issueDate)}</p>
        </div>
        <div className="text-center bg-blue-50 p-4 rounded-lg">
          <h4 className="font-semibold text-blue-600 mb-1">Data de Vencimento</h4>
          <p className="text-gray-900 font-medium">{formatDate(data.details.dueDate)}</p>
        </div>
        <div className="text-center bg-green-50 p-4 rounded-lg">
          <h4 className="font-semibold text-green-600 mb-1">Moeda</h4>
          <p className="text-gray-900 font-medium">{data.details.currency}</p>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-8 overflow-hidden rounded-lg border border-gray-200">
        <table className="w-full">
          <thead className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
            <tr>
              <th className="text-left py-4 px-4 font-semibold">Descrição</th>
              <th className="text-right py-4 px-4 font-semibold">Qtd</th>
              <th className="text-right py-4 px-4 font-semibold">Preço Unit.</th>
              <th className="text-right py-4 px-4 font-semibold">Total</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                <td className="py-3 px-4 border-b border-gray-200">{item.description}</td>
                <td className="py-3 px-4 text-right border-b border-gray-200">{formatNumber(item.quantity)}</td>
                <td className="py-3 px-4 text-right border-b border-gray-200">{formatCurrency(item.unitPrice)}</td>
                <td className="py-3 px-4 text-right border-b border-gray-200 font-semibold">{formatCurrency(item.total)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Footer with Totals and PIX */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        <div className="space-y-4">
          {qrCodeUrl && (
            <div className="bg-green-50 border border-green-200 p-4 rounded-lg text-center">
              <h4 className="font-semibold text-green-700 mb-3">Pagamento PIX</h4>
              <img src={qrCodeUrl} alt="QR Code PIX" className="w-32 h-32 mx-auto mb-2" />
              <p className="text-sm text-green-700 font-medium">{data.pix.name}</p>
              <p className="text-xs text-green-600">{data.pix.city}</p>
            </div>
          )}
        </div>

        <div className="bg-gray-50 p-6 rounded-lg">
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="text-gray-700">Subtotal:</span>
              <span className="font-medium">{formatCurrency(data.subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-700">Impostos ({data.taxRate}%):</span>
              <span className="font-medium">{formatCurrency(data.taxAmount)}</span>
            </div>
            <div className="border-t-2 border-purple-600 pt-3">
              <div className="flex justify-between">
                <span className="text-xl font-bold text-gray-900">TOTAL:</span>
                <span className="text-xl font-bold text-purple-600">{formatCurrency(data.total)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Notes and Terms */}
      {(data.details.notes || data.details.termsConditions) && (
        <div className="border-t border-gray-200 pt-6 mt-8 space-y-4">
          {data.details.notes && (
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Observações</h4>
              <p className="text-sm text-gray-700 bg-yellow-50 p-3 rounded-lg">{data.details.notes}</p>
            </div>
          )}
          
          {data.details.termsConditions && (
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Termos e Condições</h4>
              <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">{data.details.termsConditions}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}