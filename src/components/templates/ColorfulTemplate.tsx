import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function ColorfulTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-8 font-sans">
      {/* Colorful Header */}
      <div className="bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 text-white p-8 rounded-2xl mb-8 shadow-lg">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-4">
            {data.company.logo && (
              <div className="bg-white p-3 rounded-xl shadow-md">
                <img 
                  src={data.company.logo} 
                  alt="Logo" 
                  className="w-12 h-12 object-contain"
                />
              </div>
            )}
            <div>
              <h1 className="text-2xl font-bold">{data.company.name}</h1>
              <p className="text-purple-100">{data.company.website}</p>
            </div>
          </div>
          <div className="text-right bg-white bg-opacity-20 p-4 rounded-xl backdrop-blur-sm">
            <h2 className="text-xl font-bold">FATURA</h2>
            <p className="text-lg font-semibold">#{data.details.number}</p>
          </div>
        </div>
      </div>

      {/* Colorful Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-gradient-to-br from-blue-100 to-blue-200 p-6 rounded-xl border-l-4 border-blue-500">
          <h3 className="font-bold text-blue-800 mb-3 text-lg">📋 Empresa</h3>
          <div className="space-y-1 text-sm text-blue-900">
            <p className="font-semibold">{data.company.name}</p>
            <p>{data.company.address}</p>
            <p>{data.company.city}, {data.company.state} {data.company.zipCode}</p>
            <p>📞 {data.company.phone}</p>
            <p>✉️ {data.company.email}</p>
            {data.company.cnpj && <p>🏢 CNPJ: {data.company.cnpj}</p>}
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-100 to-green-200 p-6 rounded-xl border-l-4 border-green-500">
          <h3 className="font-bold text-green-800 mb-3 text-lg">👤 Cliente</h3>
          <div className="space-y-1 text-sm text-green-900">
            <p className="font-semibold">{data.client.name}</p>
            <p>{data.client.address}</p>
            <p>{data.client.city}, {data.client.state} {data.client.zipCode}</p>
            <p>📞 {data.client.phone}</p>
            <p>✉️ {data.client.email}</p>
            {data.client.cpfCnpj && <p>🆔 CPF/CNPJ: {data.client.cpfCnpj}</p>}
          </div>
        </div>
      </div>

      {/* Date Info with Colors */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        <div className="bg-gradient-to-br from-orange-100 to-orange-200 p-4 rounded-xl text-center border-l-4 border-orange-500">
          <h4 className="font-semibold text-orange-800 mb-1">📅 Emissão</h4>
          <p className="text-orange-900 font-medium">{formatDate(data.details.issueDate)}</p>
        </div>
        <div className="bg-gradient-to-br from-red-100 to-red-200 p-4 rounded-xl text-center border-l-4 border-red-500">
          <h4 className="font-semibold text-red-800 mb-1">⏰ Vencimento</h4>
          <p className="text-red-900 font-medium">{formatDate(data.details.dueDate)}</p>
        </div>
        <div className="bg-gradient-to-br from-teal-100 to-teal-200 p-4 rounded-xl text-center border-l-4 border-teal-500">
          <h4 className="font-semibold text-teal-800 mb-1">💰 Moeda</h4>
          <p className="text-teal-900 font-medium">{data.details.currency}</p>
        </div>
      </div>

      {/* Colorful Items Table */}
      <div className="mb-8 overflow-hidden rounded-xl shadow-lg">
        <table className="w-full">
          <thead className="bg-gradient-to-r from-purple-600 via-pink-600 to-red-600 text-white">
            <tr>
              <th className="text-left py-4 px-6 font-bold">🛍️ Descrição</th>
              <th className="text-right py-4 px-6 font-bold">📊 Qtd</th>
              <th className="text-right py-4 px-6 font-bold">💵 Preço Unit.</th>
              <th className="text-right py-4 px-6 font-bold">🏆 Total</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id} className={`${index % 2 === 0 ? 'bg-gradient-to-r from-purple-50 to-pink-50' : 'bg-gradient-to-r from-blue-50 to-indigo-50'} hover:from-yellow-50 hover:to-orange-50`}>
                <td className="py-4 px-6 border-b border-gray-200">{item.description}</td>
                <td className="py-4 px-6 text-right border-b border-gray-200">{formatNumber(item.quantity)}</td>
                <td className="py-4 px-6 text-right border-b border-gray-200">{formatCurrency(item.unitPrice)}</td>
                <td className="py-4 px-6 text-right border-b border-gray-200 font-semibold">{formatCurrency(item.total)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Colorful Totals and PIX */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {qrCodeUrl && (
          <div className="bg-gradient-to-br from-green-400 to-emerald-500 text-white p-6 rounded-xl text-center shadow-lg">
            <h4 className="font-bold mb-4 text-lg">💳 Pagamento PIX</h4>
            <div className="bg-white p-3 rounded-lg inline-block">
              <img src={qrCodeUrl} alt="QR Code PIX" className="w-28 h-28" />
            </div>
            <p className="font-semibold mt-3">{data.pix.name}</p>
            <p className="text-green-100 text-sm">{data.pix.city}</p>
          </div>
        )}

        <div className="bg-gradient-to-br from-gray-800 to-gray-900 text-white p-6 rounded-xl shadow-lg">
          <div className="space-y-4">
            <div className="flex justify-between">
              <span className="text-gray-300">Subtotal:</span>
              <span className="font-medium">{formatCurrency(data.subtotal)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-300">Impostos ({data.taxRate}%):</span>
              <span className="font-medium">{formatCurrency(data.taxAmount)}</span>
            </div>
            <div className="border-t border-gray-600 pt-4">
              <div className="flex justify-between">
                <span className="text-xl font-bold">TOTAL:</span>
                <span className="text-xl font-bold text-yellow-400">{formatCurrency(data.total)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Colorful Notes */}
      {(data.details.notes || data.details.termsConditions) && (
        <div className="mt-8 space-y-4">
          {data.details.notes && (
            <div className="bg-gradient-to-r from-yellow-100 to-orange-100 p-4 rounded-xl border-l-4 border-yellow-500">
              <h4 className="font-semibold text-yellow-800 mb-2">📝 Observações</h4>
              <p className="text-sm text-yellow-900">{data.details.notes}</p>
            </div>
          )}
          
          {data.details.termsConditions && (
            <div className="bg-gradient-to-r from-gray-100 to-slate-100 p-4 rounded-xl border-l-4 border-gray-500">
              <h4 className="font-semibold text-gray-800 mb-2">📋 Termos e Condições</h4>
              <p className="text-sm text-gray-700">{data.details.termsConditions}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}