import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function ElegantTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-12 font-serif relative">
      {/* Elegant Border Design */}
      <div className="absolute inset-0 border-8 border-double border-gray-300 m-4"></div>
      
      {/* <PERSON><PERSON><PERSON> Header */}
      <div className="relative z-10 text-center mb-12">
        <div className="flex justify-center mb-6">
          {data.company.logo && (
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-gold to-amber-400 rounded-full blur opacity-20"></div>
              <img 
                src={data.company.logo} 
                alt="Logo" 
                className="relative w-20 h-20 object-contain bg-white rounded-full p-3 shadow-lg border-2 border-amber-200"
              />
            </div>
          )}
        </div>
        <h1 className="text-4xl font-bold text-gray-900 mb-2 tracking-wide">{data.company.name}</h1>
        <div className="h-px bg-gradient-to-r from-transparent via-amber-400 to-transparent w-64 mx-auto mb-6"></div>
        <h2 className="text-2xl font-light text-gray-700 tracking-widest">FATURA</h2>
        <p className="text-lg font-medium text-amber-600 mt-2">№ {data.details.number}</p>
      </div>

      {/* Elegant Info Layout */}
      <div className="relative z-10 grid grid-cols-1 md:grid-cols-2 gap-12 mb-12">
        <div className="text-center">
          <div className="bg-gradient-to-b from-gray-50 to-white p-6 rounded-lg shadow-md border border-gray-200">
            <h3 className="font-bold text-gray-800 mb-4 text-lg border-b border-amber-200 pb-2">Fornecedor</h3>
            <div className="space-y-2 text-sm leading-relaxed">
              <p className="font-semibold text-gray-900">{data.company.name}</p>
              <p className="text-gray-700 italic">{data.company.address}</p>
              <p className="text-gray-700">{data.company.city} • {data.company.state} • {data.company.zipCode}</p>
              <div className="pt-2 border-t border-gray-100">
                <p className="text-gray-600">{data.company.phone}</p>
                <p className="text-amber-600">{data.company.email}</p>
                {data.company.cnpj && <p className="text-gray-600 text-xs">CNPJ: {data.company.cnpj}</p>}
              </div>
            </div>
          </div>
        </div>
        
        <div className="text-center">
          <div className="bg-gradient-to-b from-amber-50 to-white p-6 rounded-lg shadow-md border border-amber-200">
            <h3 className="font-bold text-gray-800 mb-4 text-lg border-b border-amber-200 pb-2">Cliente</h3>
            <div className="space-y-2 text-sm leading-relaxed">
              <p className="font-semibold text-gray-900">{data.client.name}</p>
              <p className="text-gray-700 italic">{data.client.address}</p>
              <p className="text-gray-700">{data.client.city} • {data.client.state} • {data.client.zipCode}</p>
              <div className="pt-2 border-t border-amber-100">
                <p className="text-gray-600">{data.client.phone}</p>
                <p className="text-amber-600">{data.client.email}</p>
                {data.client.cpfCnpj && <p className="text-gray-600 text-xs">CPF/CNPJ: {data.client.cpfCnpj}</p>}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Elegant Date Section */}
      <div className="relative z-10 text-center mb-12">
        <div className="inline-flex items-center space-x-8 bg-gradient-to-r from-gray-50 via-white to-gray-50 p-6 rounded-lg shadow-sm border border-gray-200">
          <div className="text-center">
            <p className="text-xs uppercase tracking-widest text-gray-500 mb-1">Emissão</p>
            <p className="font-semibold text-gray-900">{formatDate(data.details.issueDate)}</p>
          </div>
          <div className="w-px h-8 bg-amber-300"></div>
          <div className="text-center">
            <p className="text-xs uppercase tracking-widest text-gray-500 mb-1">Vencimento</p>
            <p className="font-semibold text-gray-900">{formatDate(data.details.dueDate)}</p>
          </div>
          <div className="w-px h-8 bg-amber-300"></div>
          <div className="text-center">
            <p className="text-xs uppercase tracking-widest text-gray-500 mb-1">Moeda</p>
            <p className="font-semibold text-gray-900">{data.details.currency}</p>
          </div>
        </div>
      </div>

      {/* Elegant Items Table */}
      <div className="relative z-10 mb-12 shadow-lg rounded-lg overflow-hidden">
        <table className="w-full">
          <thead className="bg-gradient-to-r from-amber-600 to-amber-700 text-white">
            <tr>
              <th className="text-left py-4 px-6 font-semibold tracking-wide">Descrição</th>
              <th className="text-center py-4 px-6 font-semibold tracking-wide">Quantidade</th>
              <th className="text-right py-4 px-6 font-semibold tracking-wide">Valor Unitário</th>
              <th className="text-right py-4 px-6 font-semibold tracking-wide">Total</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id} className={`${index % 2 === 0 ? 'bg-amber-50' : 'bg-white'} border-b border-amber-100`}>
                <td className="py-4 px-6 text-gray-900">{item.description}</td>
                <td className="py-4 px-6 text-center text-gray-700">{formatNumber(item.quantity)}</td>
                <td className="py-4 px-6 text-right text-gray-700">{formatCurrency(item.unitPrice)}</td>
                <td className="py-4 px-6 text-right font-semibold text-gray-900">{formatCurrency(item.total)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Elegant Totals */}
      <div className="relative z-10 flex justify-end mb-12">
        <div className="w-80 bg-gradient-to-b from-gray-50 to-white p-6 rounded-lg shadow-md border border-gray-200">
          <div className="space-y-4">
            <div className="flex justify-between pb-2 border-b border-gray-200">
              <span className="text-gray-600 italic">Subtotal</span>
              <span className="font-medium text-gray-900">{formatCurrency(data.subtotal)}</span>
            </div>
            <div className="flex justify-between pb-2 border-b border-gray-200">
              <span className="text-gray-600 italic">Impostos ({data.taxRate}%)</span>
              <span className="font-medium text-gray-900">{formatCurrency(data.taxAmount)}</span>
            </div>
            <div className="flex justify-between pt-2 border-t-2 border-amber-400">
              <span className="text-xl font-bold text-gray-900">Total Geral</span>
              <span className="text-xl font-bold text-amber-600">{formatCurrency(data.total)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Elegant PIX */}
      {qrCodeUrl && (
        <div className="relative z-10 text-center mb-8">
          <div className="inline-block bg-gradient-to-b from-green-50 to-emerald-50 p-8 rounded-lg shadow-md border border-green-200">
            <h4 className="font-bold text-green-800 mb-4 text-lg tracking-wide">Pagamento PIX</h4>
            <div className="relative inline-block">
              <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-lg transform rotate-3 opacity-20"></div>
              <img src={qrCodeUrl} alt="QR Code PIX" className="relative w-32 h-32 border-2 border-green-300 rounded-lg" />
            </div>
            <p className="font-semibold text-green-800 mt-4">{data.pix.name}</p>
            <p className="text-green-600 text-sm italic">{data.pix.city}</p>
          </div>
        </div>
      )}

      {/* Elegant Footer */}
      {(data.details.notes || data.details.termsConditions) && (
        <div className="relative z-10 space-y-6">
          {data.details.notes && (
            <div className="bg-yellow-50 border-l-4 border-yellow-400 p-6 rounded-r-lg">
              <h4 className="font-bold text-yellow-800 mb-3 italic">Observações Especiais</h4>
              <p className="text-sm text-yellow-900 leading-relaxed italic">{data.details.notes}</p>
            </div>
          )}
          
          {data.details.termsConditions && (
            <div className="bg-gray-50 border-l-4 border-gray-400 p-6 rounded-r-lg">
              <h4 className="font-bold text-gray-800 mb-3 italic">Termos e Condições</h4>
              <p className="text-sm text-gray-700 leading-relaxed">{data.details.termsConditions}</p>
            </div>
          )}
        </div>
      )}

      {/* Elegant Footer Line */}
      <div className="relative z-10 text-center mt-12 pt-6 border-t border-gray-200">
        <div className="h-px bg-gradient-to-r from-transparent via-amber-400 to-transparent w-48 mx-auto mb-4"></div>
        <p className="text-xs text-gray-500 italic tracking-wide">
          Documento gerado com elegância • {formatDate(new Date())}
        </p>
      </div>
    </div>
  );
}