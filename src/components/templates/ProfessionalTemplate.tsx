import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function ProfessionalTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-8 font-serif border border-gray-300">
      {/* Header */}
      <div className="text-center border-b-2 border-gray-800 pb-6 mb-8">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            {data.company.logo && (
              <img 
                src={data.company.logo} 
                alt="Logo" 
                className="w-20 h-20 object-contain"
              />
            )}
          </div>
          <div className="text-center flex-1">
            <h1 className="text-3xl font-bold text-gray-900 mb-2">{data.company.name}</h1>
            <h2 className="text-xl font-semibold text-gray-700 border border-gray-800 py-2 px-4 inline-block">
              FATURA COMERCIAL
            </h2>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-600">Número da Fatura</p>
            <p className="text-lg font-bold text-gray-900">{data.details.number}</p>
            <p className="text-sm text-gray-600 mt-2">Data: {formatDate(data.details.issueDate)}</p>
          </div>
        </div>
      </div>

      {/* Company and Client Info in formal layout */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        <div className="border border-gray-300 p-4">
          <h3 className="font-bold text-gray-900 mb-3 bg-gray-100 p-2 text-center">FORNECEDOR</h3>
          <div className="space-y-1 text-sm">
            <p className="font-semibold">{data.company.name}</p>
            <p>{data.company.address}</p>
            <p>{data.company.city}, {data.company.state}</p>
            <p>CEP: {data.company.zipCode}</p>
            <p>Telefone: {data.company.phone}</p>
            <p>Email: {data.company.email}</p>
            {data.company.cnpj && <p><strong>CNPJ:</strong> {data.company.cnpj}</p>}
          </div>
        </div>
        
        <div className="border border-gray-300 p-4">
          <h3 className="font-bold text-gray-900 mb-3 bg-gray-100 p-2 text-center">CLIENTE</h3>
          <div className="space-y-1 text-sm">
            <p className="font-semibold">{data.client.name}</p>
            <p>{data.client.address}</p>
            <p>{data.client.city}, {data.client.state}</p>
            <p>CEP: {data.client.zipCode}</p>
            <p>Telefone: {data.client.phone}</p>
            <p>Email: {data.client.email}</p>
            {data.client.cpfCnpj && <p><strong>CPF/CNPJ:</strong> {data.client.cpfCnpj}</p>}
          </div>
        </div>
      </div>

      {/* Invoice Details Table */}
      <div className="mb-8">
        <table className="w-full border-2 border-gray-800">
          <thead>
            <tr className="bg-gray-800 text-white">
              <th className="text-left py-3 px-4 font-bold">Data de Vencimento</th>
              <th className="text-left py-3 px-4 font-bold">Condições de Pagamento</th>
              <th className="text-left py-3 px-4 font-bold">Moeda</th>
            </tr>
          </thead>
          <tbody>
            <tr className="bg-gray-50">
              <td className="py-3 px-4 border border-gray-300 font-semibold">{formatDate(data.details.dueDate)}</td>
              <td className="py-3 px-4 border border-gray-300">30 dias</td>
              <td className="py-3 px-4 border border-gray-300">{data.details.currency}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Items Table */}
      <div className="mb-8">
        <table className="w-full border-2 border-gray-800">
          <thead>
            <tr className="bg-gray-800 text-white">
              <th className="text-left py-3 px-4 font-bold">Descrição dos Serviços/Produtos</th>
              <th className="text-center py-3 px-4 font-bold">Qtd</th>
              <th className="text-right py-3 px-4 font-bold">Valor Unit.</th>
              <th className="text-right py-3 px-4 font-bold">Valor Total</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id} className="border-b border-gray-300">
                <td className="py-3 px-4 border-r border-gray-300">{item.description}</td>
                <td className="py-3 px-4 text-center border-r border-gray-300">{formatNumber(item.quantity)}</td>
                <td className="py-3 px-4 text-right border-r border-gray-300">{formatCurrency(item.unitPrice)}</td>
                <td className="py-3 px-4 text-right font-semibold">{formatCurrency(item.total)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-80 border-2 border-gray-800">
          <table className="w-full">
            <tbody>
              <tr className="border-b border-gray-300">
                <td className="py-2 px-4 bg-gray-100 font-semibold">Subtotal:</td>
                <td className="py-2 px-4 text-right">{formatCurrency(data.subtotal)}</td>
              </tr>
              <tr className="border-b border-gray-300">
                <td className="py-2 px-4 bg-gray-100 font-semibold">Impostos ({data.taxRate}%):</td>
                <td className="py-2 px-4 text-right">{formatCurrency(data.taxAmount)}</td>
              </tr>
              <tr className="bg-gray-800 text-white">
                <td className="py-3 px-4 font-bold text-lg">TOTAL GERAL:</td>
                <td className="py-3 px-4 text-right font-bold text-lg">{formatCurrency(data.total)}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* PIX and Additional Info */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {qrCodeUrl && (
          <div className="border border-gray-300 p-4 text-center">
            <h4 className="font-bold text-gray-900 mb-3 bg-gray-100 p-2">PAGAMENTO PIX</h4>
            <img src={qrCodeUrl} alt="QR Code PIX" className="w-32 h-32 mx-auto mb-2 border border-gray-300" />
            <p className="text-sm font-semibold">{data.pix.name}</p>
            <p className="text-xs text-gray-600">{data.pix.city}</p>
            <p className="text-sm font-bold mt-2">{formatCurrency(data.total)}</p>
          </div>
        )}

        <div className="space-y-4">
          {data.details.notes && (
            <div className="border border-gray-300 p-4">
              <h4 className="font-bold text-gray-900 mb-2 bg-gray-100 p-2">OBSERVAÇÕES</h4>
              <p className="text-sm text-gray-700">{data.details.notes}</p>
            </div>
          )}
          
          {data.details.termsConditions && (
            <div className="border border-gray-300 p-4">
              <h4 className="font-bold text-gray-900 mb-2 bg-gray-100 p-2">TERMOS E CONDIÇÕES</h4>
              <p className="text-sm text-gray-700">{data.details.termsConditions}</p>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="text-center mt-8 pt-6 border-t-2 border-gray-800">
        <p className="text-sm text-gray-600">
          Esta fatura foi gerada pelo sistema Billify Generator BR - {formatDate(new Date())}
        </p>
      </div>
    </div>
  );
}