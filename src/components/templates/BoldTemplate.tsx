import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function BoldTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-8 font-sans">
      {/* Bold Header */}
      <div className="bg-black text-white p-8 mb-8 transform -skew-y-1">
        <div className="transform skew-y-1">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-6">
              {data.company.logo && (
                <div className="bg-white p-3 rounded-lg transform rotate-12">
                  <img 
                    src={data.company.logo} 
                    alt="Logo" 
                    className="w-16 h-16 object-contain"
                  />
                </div>
              )}
              <div>
                <h1 className="text-4xl font-black tracking-tight">{data.company.name}</h1>
                <p className="text-yellow-400 font-bold text-lg">{data.company.website}</p>
              </div>
            </div>
            <div className="text-right bg-red-600 p-6 rounded-lg transform -rotate-6 shadow-xl">
              <h2 className="text-3xl font-black">FATURA</h2>
              <p className="text-2xl font-bold text-yellow-300">#{data.details.number}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bold Info Blocks */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <div className="bg-red-600 text-white p-6 rounded-lg shadow-xl transform rotate-1">
          <h3 className="font-black text-xl mb-4 tracking-wide">📍 EMPRESA</h3>
          <div className="space-y-2 text-sm font-medium">
            <p className="text-yellow-200 font-bold text-lg">{data.company.name}</p>
            <p>{data.company.address}</p>
            <p>{data.company.city}, {data.company.state} {data.company.zipCode}</p>
            <p className="bg-black bg-opacity-30 p-2 rounded">📞 {data.company.phone}</p>
            <p className="bg-black bg-opacity-30 p-2 rounded">✉️ {data.company.email}</p>
            {data.company.cnpj && <p className="bg-black bg-opacity-30 p-2 rounded">🏢 {data.company.cnpj}</p>}
          </div>
        </div>
        
        <div className="bg-blue-600 text-white p-6 rounded-lg shadow-xl transform -rotate-1">
          <h3 className="font-black text-xl mb-4 tracking-wide">🎯 CLIENTE</h3>
          <div className="space-y-2 text-sm font-medium">
            <p className="text-yellow-200 font-bold text-lg">{data.client.name}</p>
            <p>{data.client.address}</p>
            <p>{data.client.city}, {data.client.state} {data.client.zipCode}</p>
            <p className="bg-black bg-opacity-30 p-2 rounded">📞 {data.client.phone}</p>
            <p className="bg-black bg-opacity-30 p-2 rounded">✉️ {data.client.email}</p>
            {data.client.cpfCnpj && <p className="bg-black bg-opacity-30 p-2 rounded">🆔 {data.client.cpfCnpj}</p>}
          </div>
        </div>
      </div>

      {/* Bold Date Badges */}
      <div className="flex justify-center space-x-12 mb-12">
        <div className="bg-yellow-400 text-black p-6 rounded-lg shadow-xl transform rotate-6">
          <h4 className="font-black text-sm uppercase tracking-widest mb-1">Emissão</h4>
          <p className="text-2xl font-black">{formatDate(data.details.issueDate)}</p>
        </div>
        <div className="bg-orange-500 text-white p-6 rounded-lg shadow-xl transform -rotate-6">
          <h4 className="font-black text-sm uppercase tracking-widest mb-1">Vencimento</h4>
          <p className="text-2xl font-black">{formatDate(data.details.dueDate)}</p>
        </div>
      </div>

      {/* Bold Items Table */}
      <div className="mb-12 shadow-2xl rounded-lg overflow-hidden transform rotate-0">
        <table className="w-full">
          <thead className="bg-gradient-to-r from-black to-gray-800 text-white">
            <tr>
              <th className="text-left py-5 px-6 font-black text-lg tracking-wide">💥 DESCRIÇÃO</th>
              <th className="text-center py-5 px-6 font-black text-lg tracking-wide">🔢 QTD</th>
              <th className="text-right py-5 px-6 font-black text-lg tracking-wide">💰 PREÇO</th>
              <th className="text-right py-5 px-6 font-black text-lg tracking-wide">🚀 TOTAL</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id} className={`${index % 2 === 0 ? 'bg-red-50' : 'bg-blue-50'} hover:bg-yellow-100 transition-colors`}>
                <td className="py-4 px-6 border-b-2 border-gray-200 font-semibold">{item.description}</td>
                <td className="py-4 px-6 text-center border-b-2 border-gray-200 font-bold text-lg">{formatNumber(item.quantity)}</td>
                <td className="py-4 px-6 text-right border-b-2 border-gray-200 font-bold">{formatCurrency(item.unitPrice)}</td>
                <td className="py-4 px-6 text-right border-b-2 border-gray-200 font-black text-lg text-red-600">{formatCurrency(item.total)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Bold Totals and PIX */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        {qrCodeUrl && (
          <div className="bg-gradient-to-br from-green-500 to-emerald-600 text-white p-8 rounded-lg shadow-2xl transform rotate-2">
            <h4 className="font-black text-2xl mb-6 tracking-wide text-center">🏆 PIX PAYMENT</h4>
            <div className="bg-white p-4 rounded-lg text-center transform -rotate-2">
              <img src={qrCodeUrl} alt="QR Code PIX" className="w-32 h-32 mx-auto" />
            </div>
            <div className="text-center mt-4">
              <p className="font-black text-lg">{data.pix.name}</p>
              <p className="text-green-100 font-bold">{data.pix.city}</p>
            </div>
          </div>
        )}

        <div className="bg-gradient-to-br from-purple-600 to-pink-600 text-white p-8 rounded-lg shadow-2xl transform -rotate-2">
          <h4 className="font-black text-2xl mb-6 tracking-wide text-center">💎 TOTAIS</h4>
          <div className="space-y-4">
            <div className="flex justify-between bg-black bg-opacity-30 p-3 rounded">
              <span className="font-bold">Subtotal:</span>
              <span className="font-black">{formatCurrency(data.subtotal)}</span>
            </div>
            <div className="flex justify-between bg-black bg-opacity-30 p-3 rounded">
              <span className="font-bold">Impostos ({data.taxRate}%):</span>
              <span className="font-black">{formatCurrency(data.taxAmount)}</span>
            </div>
            <div className="flex justify-between bg-yellow-400 text-black p-4 rounded-lg">
              <span className="text-2xl font-black">TOTAL:</span>
              <span className="text-2xl font-black">{formatCurrency(data.total)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bold Notes */}
      {(data.details.notes || data.details.termsConditions) && (
        <div className="space-y-6">
          {data.details.notes && (
            <div className="bg-gradient-to-r from-yellow-400 to-orange-400 text-black p-6 rounded-lg shadow-xl transform skew-x-1">
              <div className="transform -skew-x-1">
                <h4 className="font-black text-lg mb-3 tracking-wide">⚡ OBSERVAÇÕES IMPORTANTES</h4>
                <p className="font-semibold">{data.details.notes}</p>
              </div>
            </div>
          )}
          
          {data.details.termsConditions && (
            <div className="bg-gradient-to-r from-gray-800 to-black text-white p-6 rounded-lg shadow-xl transform -skew-x-1">
              <div className="transform skew-x-1">
                <h4 className="font-black text-lg mb-3 tracking-wide text-yellow-400">⚖️ TERMOS E CONDIÇÕES</h4>
                <p className="font-medium text-gray-200">{data.details.termsConditions}</p>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Bold Footer */}
      <div className="text-center mt-12 pt-8 border-t-4 border-black">
        <div className="bg-black text-white p-4 rounded-lg inline-block transform rotate-1">
          <p className="font-black text-sm uppercase tracking-widest">
            BILLIFY GENERATOR BR • {formatDate(new Date())}
          </p>
        </div>
      </div>
    </div>
  );
}