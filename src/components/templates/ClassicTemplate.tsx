import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function ClassicTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-8 font-serif border-4 border-gray-800">
      {/* Classic Header */}
      <div className="text-center border-b-4 border-gray-800 pb-6 mb-8">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            {data.company.logo && (
              <img 
                src={data.company.logo} 
                alt="Logo" 
                className="w-24 h-24 object-contain border-2 border-gray-800 p-2"
              />
            )}
          </div>
          <div className="text-center flex-1">
            <h1 className="text-4xl font-bold text-gray-900 mb-2">{data.company.name}</h1>
            <div className="border-2 border-gray-800 p-3 inline-block">
              <h2 className="text-2xl font-bold text-gray-800">FATURA</h2>
            </div>
          </div>
          <div className="text-right border-2 border-gray-800 p-4">
            <p className="text-sm font-bold">NÚMERO</p>
            <p className="text-xl font-bold">{data.details.number}</p>
            <p className="text-sm mt-2">DATA</p>
            <p className="font-semibold">{formatDate(data.details.issueDate)}</p>
          </div>
        </div>
      </div>

      {/* Classic Company Info */}
      <div className="mb-8 border-2 border-gray-800">
        <div className="bg-gray-800 text-white p-3">
          <h3 className="font-bold text-center">INFORMAÇÕES COMERCIAIS</h3>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2">
          <div className="p-4 border-r-2 border-gray-800">
            <h4 className="font-bold text-gray-900 mb-3">EMPRESA EMISSORA</h4>
            <div className="space-y-1 text-sm">
              <p className="font-semibold">{data.company.name}</p>
              <p>{data.company.address}</p>
              <p>{data.company.city}, {data.company.state}</p>
              <p>CEP: {data.company.zipCode}</p>
              <p>Telefone: {data.company.phone}</p>
              <p>Email: {data.company.email}</p>
              {data.company.cnpj && <p>CNPJ: {data.company.cnpj}</p>}
            </div>
          </div>
          
          <div className="p-4">
            <h4 className="font-bold text-gray-900 mb-3">CLIENTE DESTINATÁRIO</h4>
            <div className="space-y-1 text-sm">
              <p className="font-semibold">{data.client.name}</p>
              <p>{data.client.address}</p>
              <p>{data.client.city}, {data.client.state}</p>
              <p>CEP: {data.client.zipCode}</p>
              <p>Telefone: {data.client.phone}</p>
              <p>Email: {data.client.email}</p>
              {data.client.cpfCnpj && <p>CPF/CNPJ: {data.client.cpfCnpj}</p>}
            </div>
          </div>
        </div>
      </div>

      {/* Classic Details Table */}
      <div className="mb-8 border-2 border-gray-800">
        <div className="bg-gray-800 text-white p-3">
          <h3 className="font-bold text-center">DETALHES DA FATURA</h3>
        </div>
        <table className="w-full">
          <tbody>
            <tr>
              <td className="py-3 px-4 border-r border-gray-800 bg-gray-100 font-bold">Data de Vencimento:</td>
              <td className="py-3 px-4 border-r border-gray-800">{formatDate(data.details.dueDate)}</td>
              <td className="py-3 px-4 bg-gray-100 font-bold">Moeda:</td>
              <td className="py-3 px-4">{data.details.currency}</td>
            </tr>
          </tbody>
        </table>
      </div>

      {/* Classic Items Table */}
      <div className="mb-8 border-2 border-gray-800">
        <div className="bg-gray-800 text-white p-3">
          <h3 className="font-bold text-center">DISCRIMINAÇÃO DOS ITENS</h3>
        </div>
        <table className="w-full">
          <thead className="bg-gray-200">
            <tr>
              <th className="text-left py-3 px-4 font-bold border-r border-gray-800">DESCRIÇÃO</th>
              <th className="text-center py-3 px-4 font-bold border-r border-gray-800">QTD</th>
              <th className="text-right py-3 px-4 font-bold border-r border-gray-800">VALOR UNIT.</th>
              <th className="text-right py-3 px-4 font-bold">VALOR TOTAL</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                <td className="py-3 px-4 border-r border-gray-300 border-b border-gray-300">{item.description}</td>
                <td className="py-3 px-4 text-center border-r border-gray-300 border-b border-gray-300">{formatNumber(item.quantity)}</td>
                <td className="py-3 px-4 text-right border-r border-gray-300 border-b border-gray-300">{formatCurrency(item.unitPrice)}</td>
                <td className="py-3 px-4 text-right border-b border-gray-300 font-semibold">{formatCurrency(item.total)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Classic Totals */}
      <div className="flex justify-end mb-8">
        <div className="w-96 border-2 border-gray-800">
          <div className="bg-gray-800 text-white p-3">
            <h3 className="font-bold text-center">RESUMO FINANCEIRO</h3>
          </div>
          <table className="w-full">
            <tbody>
              <tr className="bg-gray-100">
                <td className="py-3 px-4 font-bold border-r border-gray-800">SUBTOTAL:</td>
                <td className="py-3 px-4 text-right font-semibold">{formatCurrency(data.subtotal)}</td>
              </tr>
              <tr className="bg-white">
                <td className="py-3 px-4 font-bold border-r border-gray-800">IMPOSTOS ({data.taxRate}%):</td>
                <td className="py-3 px-4 text-right font-semibold">{formatCurrency(data.taxAmount)}</td>
              </tr>
              <tr className="bg-gray-800 text-white">
                <td className="py-4 px-4 font-bold text-lg border-r border-gray-600">TOTAL GERAL:</td>
                <td className="py-4 px-4 text-right font-bold text-xl">{formatCurrency(data.total)}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* PIX Payment */}
      {qrCodeUrl && (
        <div className="border-2 border-gray-800 mb-8">
          <div className="bg-gray-800 text-white p-3">
            <h3 className="font-bold text-center">PAGAMENTO PIX</h3>
          </div>
          <div className="p-6 text-center">
            <img src={qrCodeUrl} alt="QR Code PIX" className="w-32 h-32 mx-auto mb-3 border-2 border-gray-800" />
            <p className="font-bold text-gray-900">{data.pix.name}</p>
            <p className="text-gray-700">{data.pix.city}</p>
            <p className="font-bold text-lg mt-2">{formatCurrency(data.total)}</p>
          </div>
        </div>
      )}

      {/* Classic Footer */}
      <div className="border-t-4 border-gray-800 pt-6 text-center">
        <p className="text-sm text-gray-600 font-semibold">
          Esta fatura foi gerada automaticamente em {formatDate(new Date())}
        </p>
        <p className="text-xs text-gray-500 mt-2">
          Billify Generator BR - Sistema de Geração de Faturas Profissionais
        </p>
      </div>
    </div>
  );
}