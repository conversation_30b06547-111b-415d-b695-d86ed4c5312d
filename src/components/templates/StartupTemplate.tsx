import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function StartupTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-8 font-mono relative">
      {/* Tech Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="grid grid-cols-20 grid-rows-20 h-full">
          {[...Array(400)].map((_, i) => (
            <div key={i} className="border border-gray-300"></div>
          ))}
        </div>
      </div>

      {/* Startup Header */}
      <div className="relative z-10 bg-gradient-to-r from-cyan-500 to-blue-500 text-white p-6 rounded-lg mb-8 shadow-lg">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-4">
            {data.company.logo && (
              <div className="bg-white p-2 rounded-lg">
                <img 
                  src={data.company.logo} 
                  alt="Logo" 
                  className="w-14 h-14 object-contain"
                />
              </div>
            )}
            <div>
              <h1 className="text-2xl font-bold">{data.company.name}</h1>
              <p className="text-cyan-100 font-mono text-sm">{data.company.website}</p>
            </div>
          </div>
          <div className="text-right bg-black bg-opacity-20 p-4 rounded-lg backdrop-blur-sm font-mono">
            <p className="text-cyan-200 text-sm">// INVOICE</p>
            <p className="text-xl font-bold">#{data.details.number}</p>
            <p className="text-cyan-200 text-xs mt-1">v1.0.0</p>
          </div>
        </div>
      </div>

      {/* Tech-style Info Cards */}
      <div className="relative z-10 grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-gray-900 text-green-400 p-6 rounded-lg font-mono text-sm shadow-lg">
          <p className="text-green-300 mb-3">{`> FROM_COMPANY {`}</p>
          <div className="pl-4 space-y-1 text-green-400">
            <p>name: "{data.company.name}",</p>
            <p>address: "{data.company.address}",</p>
            <p>city: "{data.company.city}, {data.company.state}",</p>
            <p>zipCode: "{data.company.zipCode}",</p>
            <p>phone: "{data.company.phone}",</p>
            <p>email: "{data.company.email}",</p>
            {data.company.cnpj && <p>cnpj: "{data.company.cnpj}",</p>}
          </div>
          <p className="text-green-300 mt-3">{`}`}</p>
        </div>
        
        <div className="bg-gray-900 text-blue-400 p-6 rounded-lg font-mono text-sm shadow-lg">
          <p className="text-blue-300 mb-3">{`> TO_CLIENT {`}</p>
          <div className="pl-4 space-y-1 text-blue-400">
            <p>name: "{data.client.name}",</p>
            <p>address: "{data.client.address}",</p>
            <p>city: "{data.client.city}, {data.client.state}",</p>
            <p>zipCode: "{data.client.zipCode}",</p>
            <p>phone: "{data.client.phone}",</p>
            <p>email: "{data.client.email}",</p>
            {data.client.cpfCnpj && <p>cpfCnpj: "{data.client.cpfCnpj}",</p>}
          </div>
          <p className="text-blue-300 mt-3">{`}`}</p>
        </div>
      </div>

      {/* Code-style Dates */}
      <div className="relative z-10 bg-gray-100 p-4 rounded-lg mb-8 font-mono text-sm">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <span className="text-purple-600">const</span> <span className="text-blue-600">issueDate</span> <span className="text-gray-600">=</span> <span className="text-green-600">"{formatDate(data.details.issueDate)}"</span>
          </div>
          <div>
            <span className="text-purple-600">const</span> <span className="text-blue-600">dueDate</span> <span className="text-gray-600">=</span> <span className="text-green-600">"{formatDate(data.details.dueDate)}"</span>
          </div>
          <div>
            <span className="text-purple-600">const</span> <span className="text-blue-600">currency</span> <span className="text-gray-600">=</span> <span className="text-green-600">"{data.details.currency}"</span>
          </div>
        </div>
      </div>

      {/* Tech Items Table */}
      <div className="relative z-10 mb-8 bg-gray-900 rounded-lg overflow-hidden shadow-lg">
        <div className="bg-gradient-to-r from-cyan-600 to-blue-600 text-white p-4">
          <h3 className="font-bold text-center font-mono">// ITEMS_ARRAY</h3>
        </div>
        <table className="w-full text-green-400 font-mono text-sm">
          <thead className="bg-gray-800">
            <tr>
              <th className="text-left py-3 px-4 text-cyan-400">description</th>
              <th className="text-right py-3 px-4 text-cyan-400">qty</th>
              <th className="text-right py-3 px-4 text-cyan-400">price</th>
              <th className="text-right py-3 px-4 text-cyan-400">total</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-850' : 'bg-gray-900'}>
                <td className="py-3 px-4 text-green-400">"{item.description}",</td>
                <td className="py-3 px-4 text-right text-yellow-400">{formatNumber(item.quantity)},</td>
                <td className="py-3 px-4 text-right text-orange-400">{formatCurrency(item.unitPrice)},</td>
                <td className="py-3 px-4 text-right text-red-400 font-bold">{formatCurrency(item.total)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Tech Totals */}
      <div className="relative z-10 flex justify-end mb-8">
        <div className="bg-gray-900 text-green-400 p-6 rounded-lg font-mono text-sm w-80 shadow-lg">
          <p className="text-cyan-400 mb-3">{`> CALCULATE_TOTALS() {`}</p>
          <div className="pl-4 space-y-2">
            <div className="flex justify-between">
              <span className="text-gray-400">subtotal:</span>
              <span className="text-yellow-400">{formatCurrency(data.subtotal)},</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-400">tax({data.taxRate}%):</span>
              <span className="text-orange-400">{formatCurrency(data.taxAmount)},</span>
            </div>
            <div className="flex justify-between pt-2 border-t border-gray-700">
              <span className="text-white font-bold">TOTAL:</span>
              <span className="text-red-400 font-bold text-lg">{formatCurrency(data.total)}</span>
            </div>
          </div>
          <p className="text-cyan-400 mt-3">{`}`}</p>
        </div>
      </div>

      {/* PIX QR */}
      {qrCodeUrl && (
        <div className="relative z-10 bg-gradient-to-r from-green-400 to-emerald-500 text-white p-6 rounded-lg text-center mb-8 shadow-lg">
          <h4 className="font-bold mb-4 font-mono">// PIX_PAYMENT</h4>
          <div className="bg-white p-4 rounded-lg inline-block">
            <img src={qrCodeUrl} alt="QR Code PIX" className="w-28 h-28" />
          </div>
          <p className="font-mono mt-3 text-sm">recipient: "{data.pix.name}"</p>
          <p className="font-mono text-green-100 text-xs">location: "{data.pix.city}"</p>
        </div>
      )}

      {/* Code Comments for Notes */}
      {(data.details.notes || data.details.termsConditions) && (
        <div className="relative z-10 bg-gray-100 p-6 rounded-lg font-mono text-sm space-y-4">
          {data.details.notes && (
            <div>
              <p className="text-green-600 font-bold">/* OBSERVAÇÕES */</p>
              <p className="text-gray-800 ml-4">{data.details.notes}</p>
            </div>
          )}
          
          {data.details.termsConditions && (
            <div>
              <p className="text-blue-600 font-bold">/* TERMOS E CONDIÇÕES */</p>
              <p className="text-gray-800 ml-4">{data.details.termsConditions}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}