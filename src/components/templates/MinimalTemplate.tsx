import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function MinimalTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-12 font-light">
      {/* Minimal Header */}
      <div className="flex justify-between items-start mb-16">
        <div className="flex items-center space-x-6">
          {data.company.logo && (
            <img 
              src={data.company.logo} 
              alt="Logo" 
              className="w-12 h-12 object-contain"
            />
          )}
          <div>
            <h1 className="text-2xl font-light text-gray-900">{data.company.name}</h1>
          </div>
        </div>
        <div className="text-right">
          <p className="text-4xl font-thin text-gray-400">FATURA</p>
          <p className="text-xl font-light text-gray-900 mt-2">#{data.details.number}</p>
        </div>
      </div>

      {/* Clean Address Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-16 mb-16">
        <div className="space-y-2">
          <p className="text-xs uppercase tracking-wide text-gray-500 mb-4">De</p>
          <div className="space-y-1 text-sm leading-relaxed">
            <p className="font-medium">{data.company.name}</p>
            <p className="text-gray-600">{data.company.address}</p>
            <p className="text-gray-600">{data.company.city}, {data.company.state} {data.company.zipCode}</p>
            <p className="text-gray-600">{data.company.email}</p>
          </div>
        </div>
        
        <div className="space-y-2">
          <p className="text-xs uppercase tracking-wide text-gray-500 mb-4">Para</p>
          <div className="space-y-1 text-sm leading-relaxed">
            <p className="font-medium">{data.client.name}</p>
            <p className="text-gray-600">{data.client.address}</p>
            <p className="text-gray-600">{data.client.city}, {data.client.state} {data.client.zipCode}</p>
            <p className="text-gray-600">{data.client.email}</p>
          </div>
        </div>
      </div>

      {/* Minimal Date Info */}
      <div className="flex justify-between text-sm mb-16">
        <div>
          <p className="text-gray-500">Emissão</p>
          <p className="font-medium">{formatDate(data.details.issueDate)}</p>
        </div>
        <div>
          <p className="text-gray-500">Vencimento</p>
          <p className="font-medium">{formatDate(data.details.dueDate)}</p>
        </div>
      </div>

      {/* Clean Items List */}
      <div className="mb-16">
        <div className="border-b border-gray-300 pb-2 mb-6">
          <div className="grid grid-cols-12 gap-4 text-xs uppercase tracking-wide text-gray-500">
            <div className="col-span-6">Descrição</div>
            <div className="col-span-2 text-center">Quantidade</div>
            <div className="col-span-2 text-right">Preço Unit.</div>
            <div className="col-span-2 text-right">Total</div>
          </div>
        </div>
        
        {data.items.map((item) => (
          <div key={item.id} className="grid grid-cols-12 gap-4 py-4 border-b border-gray-100 text-sm">
            <div className="col-span-6 text-gray-900">{item.description}</div>
            <div className="col-span-2 text-center text-gray-600">{formatNumber(item.quantity)}</div>
            <div className="col-span-2 text-right text-gray-600">{formatCurrency(item.unitPrice)}</div>
            <div className="col-span-2 text-right font-medium text-gray-900">{formatCurrency(item.total)}</div>
          </div>
        ))}
      </div>

      {/* Minimal Totals */}
      <div className="flex justify-end mb-16">
        <div className="w-80 space-y-3 text-sm">
          <div className="flex justify-between py-2">
            <span className="text-gray-600">Subtotal</span>
            <span className="font-medium">{formatCurrency(data.subtotal)}</span>
          </div>
          <div className="flex justify-between py-2">
            <span className="text-gray-600">Impostos ({data.taxRate}%)</span>
            <span className="font-medium">{formatCurrency(data.taxAmount)}</span>
          </div>
          <div className="border-t border-gray-300 pt-3">
            <div className="flex justify-between">
              <span className="text-lg font-light">Total</span>
              <span className="text-lg font-light">{formatCurrency(data.total)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* PIX Payment */}
      {qrCodeUrl && (
        <div className="text-center mb-8">
          <p className="text-xs uppercase tracking-wide text-gray-500 mb-4">Pagamento PIX</p>
          <img src={qrCodeUrl} alt="QR Code PIX" className="w-24 h-24 mx-auto mb-2" />
          <p className="text-sm text-gray-600">{data.pix.name}</p>
        </div>
      )}

      {/* Footer Notes */}
      <div className="space-y-8 text-sm text-gray-600 leading-relaxed">
        {data.details.notes && (
          <div>
            <p className="text-xs uppercase tracking-wide text-gray-500 mb-2">Observações</p>
            <p>{data.details.notes}</p>
          </div>
        )}
        
        {data.details.termsConditions && (
          <div>
            <p className="text-xs uppercase tracking-wide text-gray-500 mb-2">Termos e Condições</p>
            <p>{data.details.termsConditions}</p>
          </div>
        )}
      </div>
    </div>
  );
}