import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function CorporateTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-8 font-sans">
      {/* Corporate Header */}
      <div className="bg-navy-900 text-white p-8 mb-8" style={{ backgroundColor: '#1e3a8a' }}>
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-6">
            {data.company.logo && (
              <div className="bg-white p-3 rounded-lg">
                <img 
                  src={data.company.logo} 
                  alt="Logo" 
                  className="w-16 h-16 object-contain"
                />
              </div>
            )}
            <div>
              <h1 className="text-3xl font-bold tracking-wide">{data.company.name}</h1>
              <p className="text-blue-200 text-lg">{data.company.website}</p>
            </div>
          </div>
          <div className="text-right border-l-2 border-blue-300 pl-8">
            <h2 className="text-2xl font-bold mb-2">INVOICE</h2>
            <p className="text-xl font-semibold text-blue-200">#{data.details.number}</p>
            <p className="text-sm text-blue-300 mt-2">{formatDate(data.details.issueDate)}</p>
          </div>
        </div>
      </div>

      {/* Corporate Info Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-gray-50 p-6 border-l-4 border-blue-600">
          <h3 className="font-bold text-blue-800 mb-3 uppercase tracking-wide text-sm">Billed From</h3>
          <div className="space-y-1 text-sm text-gray-800">
            <p className="font-semibold">{data.company.name}</p>
            <p>{data.company.address}</p>
            <p>{data.company.city}, {data.company.state} {data.company.zipCode}</p>
            <p>{data.company.phone}</p>
            <p>{data.company.email}</p>
            {data.company.cnpj && <p>CNPJ: {data.company.cnpj}</p>}
          </div>
        </div>
        
        <div className="bg-gray-50 p-6 border-l-4 border-green-600">
          <h3 className="font-bold text-green-800 mb-3 uppercase tracking-wide text-sm">Billed To</h3>
          <div className="space-y-1 text-sm text-gray-800">
            <p className="font-semibold">{data.client.name}</p>
            <p>{data.client.address}</p>
            <p>{data.client.city}, {data.client.state} {data.client.zipCode}</p>
            <p>{data.client.phone}</p>
            <p>{data.client.email}</p>
            {data.client.cpfCnpj && <p>CPF/CNPJ: {data.client.cpfCnpj}</p>}
          </div>
        </div>

        <div className="bg-gray-50 p-6 border-l-4 border-orange-600">
          <h3 className="font-bold text-orange-800 mb-3 uppercase tracking-wide text-sm">Payment Details</h3>
          <div className="space-y-2 text-sm text-gray-800">
            <div className="flex justify-between">
              <span>Due Date:</span>
              <span className="font-semibold">{formatDate(data.details.dueDate)}</span>
            </div>
            <div className="flex justify-between">
              <span>Currency:</span>
              <span className="font-semibold">{data.details.currency}</span>
            </div>
            <div className="flex justify-between">
              <span>Amount:</span>
              <span className="font-bold text-orange-600">{formatCurrency(data.total)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Corporate Items Table */}
      <div className="mb-8">
        <table className="w-full border border-gray-300">
          <thead>
            <tr style={{ backgroundColor: '#1e3a8a' }} className="text-white">
              <th className="text-left py-4 px-6 font-bold uppercase tracking-wide">Description</th>
              <th className="text-center py-4 px-6 font-bold uppercase tracking-wide">Qty</th>
              <th className="text-right py-4 px-6 font-bold uppercase tracking-wide">Rate</th>
              <th className="text-right py-4 px-6 font-bold uppercase tracking-wide">Amount</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id} className={index % 2 === 0 ? 'bg-gray-50' : 'bg-white'}>
                <td className="py-4 px-6 border-b border-gray-200 text-gray-900">{item.description}</td>
                <td className="py-4 px-6 text-center border-b border-gray-200 text-gray-700">{formatNumber(item.quantity)}</td>
                <td className="py-4 px-6 text-right border-b border-gray-200 text-gray-700">{formatCurrency(item.unitPrice)}</td>
                <td className="py-4 px-6 text-right border-b border-gray-200 font-semibold text-gray-900">{formatCurrency(item.total)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Corporate Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
        {qrCodeUrl && (
          <div className="bg-green-50 border border-green-200 p-6 text-center">
            <h4 className="font-bold text-green-800 mb-4 uppercase tracking-wide">PIX Payment</h4>
            <img src={qrCodeUrl} alt="QR Code PIX" className="w-32 h-32 mx-auto mb-3 border-2 border-green-300" />
            <p className="font-semibold text-green-800">{data.pix.name}</p>
            <p className="text-green-600 text-sm">{data.pix.city}</p>
          </div>
        )}

        <div className="bg-gray-800 text-white p-6">
          <h4 className="font-bold mb-4 uppercase tracking-wide text-center">Invoice Summary</h4>
          <div className="space-y-3">
            <div className="flex justify-between border-b border-gray-600 pb-2">
              <span className="text-gray-300">Subtotal:</span>
              <span className="font-medium">{formatCurrency(data.subtotal)}</span>
            </div>
            <div className="flex justify-between border-b border-gray-600 pb-2">
              <span className="text-gray-300">Tax ({data.taxRate}%):</span>
              <span className="font-medium">{formatCurrency(data.taxAmount)}</span>
            </div>
            <div className="flex justify-between pt-3 border-t-2 border-blue-400">
              <span className="text-xl font-bold">TOTAL:</span>
              <span className="text-xl font-bold text-blue-300">{formatCurrency(data.total)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Corporate Footer */}
      <div className="mt-12 text-center border-t border-gray-300 pt-6">
        <div className="space-y-4">
          {data.details.notes && (
            <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg">
              <h4 className="font-bold text-yellow-800 mb-2 uppercase tracking-wide text-sm">Notes</h4>
              <p className="text-sm text-yellow-900">{data.details.notes}</p>
            </div>
          )}
          
          {data.details.termsConditions && (
            <div className="bg-gray-50 border border-gray-200 p-4 rounded-lg">
              <h4 className="font-bold text-gray-800 mb-2 uppercase tracking-wide text-sm">Terms & Conditions</h4>
              <p className="text-sm text-gray-700">{data.details.termsConditions}</p>
            </div>
          )}
        </div>
        
        <div className="mt-8 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 uppercase tracking-wide">
            Generated by Billify Generator BR • {formatDate(new Date())}
          </p>
        </div>
      </div>
    </div>
  );
}