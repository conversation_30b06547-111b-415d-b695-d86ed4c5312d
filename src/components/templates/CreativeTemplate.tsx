import React, { useEffect, useState } from 'react';
import { InvoiceData } from '../../types/invoice';
import { formatCurrency, formatDate, formatNumber } from '../../utils/invoice';
import { generatePixQRCode } from '../../utils/pix';

interface TemplateProps {
  data: InvoiceData;
}

export function CreativeTemplate({ data }: TemplateProps) {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    if (data.pix.key && data.pix.name && data.pix.city && data.total > 0) {
      generatePixQRCode(data.pix)
        .then(setQrCodeUrl)
        .catch(console.error);
    }
  }, [data.pix, data.total]);

  return (
    <div className="w-full max-w-4xl mx-auto bg-white p-8 font-sans relative overflow-hidden">
      {/* Creative Background Elements */}
      <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-purple-200 to-transparent rounded-full opacity-30 -mr-32 -mt-32"></div>
      <div className="absolute bottom-0 left-0 w-48 h-48 bg-gradient-to-tr from-blue-200 to-transparent rounded-full opacity-30 -ml-24 -mb-24"></div>
      
      {/* Creative Header */}
      <div className="relative z-10 mb-12">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-6">
            {data.company.logo && (
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400 to-blue-400 rounded-lg transform rotate-3"></div>
                <img 
                  src={data.company.logo} 
                  alt="Logo" 
                  className="relative w-16 h-16 object-contain bg-white p-2 rounded-lg shadow-lg"
                />
              </div>
            )}
            <div>
              <h1 className="text-3xl font-bold text-gray-900">{data.company.name}</h1>
              <p className="text-purple-600 font-medium">{data.company.website}</p>
            </div>
          </div>
          <div className="text-right bg-gradient-to-br from-purple-500 to-blue-500 text-white p-6 rounded-2xl shadow-lg transform -rotate-2">
            <h2 className="text-xl font-bold">FATURA</h2>
            <p className="text-lg font-semibold">#{data.details.number}</p>
          </div>
        </div>
      </div>

      {/* Creative Info Sections */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12 relative z-10">
        <div className="bg-gradient-to-br from-purple-50 to-blue-50 p-6 rounded-2xl border-l-4 border-purple-500 shadow-md">
          <h3 className="font-bold text-purple-700 mb-4 text-lg flex items-center">
            <span className="bg-purple-500 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm mr-3">DE</span>
            Empresa
          </h3>
          <div className="space-y-2 text-sm">
            <p className="font-semibold text-gray-900">{data.company.name}</p>
            <p className="text-gray-700">{data.company.address}</p>
            <p className="text-gray-700">{data.company.city}, {data.company.state} {data.company.zipCode}</p>
            <div className="flex items-center space-x-2">
              <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs">📞</span>
              <span>{data.company.phone}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs">✉️</span>
              <span>{data.company.email}</span>
            </div>
            {data.company.cnpj && (
              <div className="flex items-center space-x-2">
                <span className="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs">🏢</span>
                <span>CNPJ: {data.company.cnpj}</span>
              </div>
            )}
          </div>
        </div>
        
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 p-6 rounded-2xl border-l-4 border-green-500 shadow-md">
          <h3 className="font-bold text-green-700 mb-4 text-lg flex items-center">
            <span className="bg-green-500 text-white w-8 h-8 rounded-full flex items-center justify-center text-sm mr-3">P/</span>
            Cliente
          </h3>
          <div className="space-y-2 text-sm">
            <p className="font-semibold text-gray-900">{data.client.name}</p>
            <p className="text-gray-700">{data.client.address}</p>
            <p className="text-gray-700">{data.client.city}, {data.client.state} {data.client.zipCode}</p>
            <div className="flex items-center space-x-2">
              <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs">📞</span>
              <span>{data.client.phone}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs">✉️</span>
              <span>{data.client.email}</span>
            </div>
            {data.client.cpfCnpj && (
              <div className="flex items-center space-x-2">
                <span className="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs">🆔</span>
                <span>CPF/CNPJ: {data.client.cpfCnpj}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Creative Date Badges */}
      <div className="flex justify-center space-x-8 mb-12 relative z-10">
        <div className="bg-orange-100 border-2 border-orange-300 p-4 rounded-full text-center transform -rotate-6 shadow-lg">
          <p className="text-xs font-bold text-orange-600 uppercase">Emissão</p>
          <p className="text-sm font-bold text-orange-800">{formatDate(data.details.issueDate)}</p>
        </div>
        <div className="bg-red-100 border-2 border-red-300 p-4 rounded-full text-center transform rotate-6 shadow-lg">
          <p className="text-xs font-bold text-red-600 uppercase">Vencimento</p>
          <p className="text-sm font-bold text-red-800">{formatDate(data.details.dueDate)}</p>
        </div>
      </div>

      {/* Creative Items Table */}
      <div className="mb-12 relative z-10 shadow-xl rounded-2xl overflow-hidden">
        <table className="w-full">
          <thead className="bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white">
            <tr>
              <th className="text-left py-4 px-6 font-bold">🎨 Descrição</th>
              <th className="text-right py-4 px-6 font-bold">📦 Qtd</th>
              <th className="text-right py-4 px-6 font-bold">💎 Preço Unit.</th>
              <th className="text-right py-4 px-6 font-bold">✨ Total</th>
            </tr>
          </thead>
          <tbody>
            {data.items.map((item, index) => (
              <tr key={item.id} className={`${index % 2 === 0 ? 'bg-gradient-to-r from-gray-50 to-purple-50' : 'bg-gradient-to-r from-blue-50 to-indigo-50'}`}>
                <td className="py-4 px-6 border-b border-gray-200">{item.description}</td>
                <td className="py-4 px-6 text-right border-b border-gray-200">{formatNumber(item.quantity)}</td>
                <td className="py-4 px-6 text-right border-b border-gray-200">{formatCurrency(item.unitPrice)}</td>
                <td className="py-4 px-6 text-right border-b border-gray-200 font-semibold">{formatCurrency(item.total)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Creative Footer */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 relative z-10">
        {qrCodeUrl && (
          <div className="bg-gradient-to-br from-green-400 to-emerald-500 text-white p-6 rounded-2xl text-center shadow-xl transform rotate-1">
            <h4 className="font-bold mb-4 text-lg">🚀 Pagamento PIX</h4>
            <div className="bg-white p-4 rounded-xl inline-block transform -rotate-1">
              <img src={qrCodeUrl} alt="QR Code PIX" className="w-28 h-28" />
            </div>
            <p className="font-semibold mt-3">{data.pix.name}</p>
            <p className="text-green-100 text-sm">{data.pix.city}</p>
          </div>
        )}

        <div className="bg-gradient-to-br from-gray-700 to-gray-900 text-white p-6 rounded-2xl shadow-xl transform -rotate-1">
          <h4 className="font-bold mb-4 text-lg text-center">💰 Totais</h4>
          <div className="space-y-3">
            <div className="flex justify-between border-b border-gray-600 pb-2">
              <span className="text-gray-300">Subtotal:</span>
              <span className="font-medium">{formatCurrency(data.subtotal)}</span>
            </div>
            <div className="flex justify-between border-b border-gray-600 pb-2">
              <span className="text-gray-300">Impostos ({data.taxRate}%):</span>
              <span className="font-medium">{formatCurrency(data.taxAmount)}</span>
            </div>
            <div className="flex justify-between pt-2">
              <span className="text-xl font-bold text-yellow-400">TOTAL:</span>
              <span className="text-xl font-bold text-yellow-400">{formatCurrency(data.total)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Creative Notes */}
      {(data.details.notes || data.details.termsConditions) && (
        <div className="mt-12 space-y-6 relative z-10">
          {data.details.notes && (
            <div className="bg-gradient-to-r from-yellow-100 via-orange-100 to-red-100 p-6 rounded-2xl border-l-4 border-yellow-500 shadow-md transform rotate-0.5">
              <h4 className="font-bold text-yellow-800 mb-3 flex items-center">
                <span className="bg-yellow-500 text-white w-6 h-6 rounded-full flex items-center justify-center text-xs mr-2">!</span>
                Observações
              </h4>
              <p className="text-sm text-yellow-900">{data.details.notes}</p>
            </div>
          )}
          
          {data.details.termsConditions && (
            <div className="bg-gradient-to-r from-blue-100 via-indigo-100 to-purple-100 p-6 rounded-2xl border-l-4 border-blue-500 shadow-md transform -rotate-0.5">
              <h4 className="font-bold text-blue-800 mb-3 flex items-center">
                <span className="bg-blue-500 text-white w-6 h-6 rounded-full flex items-center justify-center text-xs mr-2">T</span>
                Termos e Condições
              </h4>
              <p className="text-sm text-blue-900">{data.details.termsConditions}</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}