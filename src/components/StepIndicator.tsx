import React from 'react';
import { Check } from 'lucide-react';
import { InvoiceStep } from '../types/invoice';

interface Step {
  id: InvoiceStep;
  title: string;
  description: string;
}

const steps: Step[] = [
  { id: 'details', title: 'Detalhes', description: 'Informações da fatura' },
  { id: 'parties', title: 'Empresas', description: 'Dados empresa e cliente' },
  { id: 'shipping', title: 'Entrega', description: 'Informações de envio' },
  { id: 'items', title: 'Itens', description: 'Lista de produtos/serviços' },
  { id: 'preview', title: 'Preview', description: 'Visualização e template' },
];

interface StepIndicatorProps {
  currentStep: InvoiceStep;
  onStepClick: (step: InvoiceStep) => void;
  completedSteps: InvoiceStep[];
}

export function StepIndicator({ currentStep, onStepClick, completedSteps }: StepIndicatorProps) {
  const currentStepIndex = steps.findIndex(step => step.id === currentStep);
  
  return (
    <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
      <div className="flex items-center justify-between">
        {steps.map((step, index) => {
          const isCompleted = completedSteps.includes(step.id);
          const isCurrent = step.id === currentStep;
          const isClickable = index <= currentStepIndex;
          
          return (
            <React.Fragment key={step.id}>
              <div 
                className={`flex flex-col items-center cursor-pointer transition-all duration-200 ${
                  isClickable ? 'hover:scale-105' : 'cursor-not-allowed'
                }`}
                onClick={() => isClickable && onStepClick(step.id)}
              >
                <div className={`
                  w-12 h-12 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-300
                  ${isCurrent 
                    ? 'bg-gradient-to-r from-purple-600 to-blue-600 text-white shadow-lg' 
                    : isCompleted 
                    ? 'bg-green-500 text-white' 
                    : 'bg-gray-200 text-gray-500'
                  }
                `}>
                  {isCompleted ? <Check className="w-5 h-5" /> : index + 1}
                </div>
                <div className="text-center mt-2">
                  <div className={`text-sm font-medium ${
                    isCurrent ? 'text-purple-600' : isCompleted ? 'text-green-600' : 'text-gray-500'
                  }`}>
                    {step.title}
                  </div>
                  <div className="text-xs text-gray-400 mt-1">
                    {step.description}
                  </div>
                </div>
              </div>
              
              {index < steps.length - 1 && (
                <div className={`h-0.5 flex-1 mx-4 transition-all duration-300 ${
                  index < currentStepIndex ? 'bg-green-500' : 'bg-gray-200'
                }`} />
              )}
            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
}