import React from 'react';
import { <PERSON><PERSON>ronLeft, ChevronRight, Eye } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useInvoice } from '../context/InvoiceContext';
import { StepIndicator } from './StepIndicator';
import { DetailsStep } from './steps/DetailsStep';
import { PartiesStep } from './steps/PartiesStep';
import { ShippingStep } from './steps/ShippingStep';
import { ItemsStep } from './steps/ItemsStep';
import { PreviewStep } from './steps/PreviewStep';
import { InvoiceStep } from '../types/invoice';

const stepOrder: InvoiceStep[] = ['details', 'parties', 'shipping', 'items', 'preview'];

export function InvoiceCreator() {
  const { state, dispatch } = useInvoice();
  const navigate = useNavigate();
  const currentStepIndex = stepOrder.indexOf(state.currentStep);

  const getCompletedSteps = (): InvoiceStep[] => {
    const completed: InvoiceStep[] = [];
    
    // Details validation
    if (state.data.details.number && state.data.details.issueDate && state.data.details.dueDate) {
      completed.push('details');
    }
    
    // Parties validation
    if (state.data.company.name && state.data.company.email && 
        state.data.client.name && state.data.client.email) {
      completed.push('parties');
    }
    
    // Shipping is always considered complete (optional)
    completed.push('shipping');
    
    // Items validation
    if (state.data.items.length > 0) {
      completed.push('items');
    }
    
    // Preview is complete when all others are done
    if (completed.length >= 4) {
      completed.push('preview');
    }
    
    return completed;
  };

  const completedSteps = getCompletedSteps();
  const canProceed = completedSteps.includes(state.currentStep);
  const isLastStep = currentStepIndex === stepOrder.length - 1;

  const handleNext = () => {
    if (canProceed && !isLastStep) {
      const nextStep = stepOrder[currentStepIndex + 1];
      dispatch({ type: 'SET_STEP', payload: nextStep });
    }
  };

  const handlePrevious = () => {
    if (currentStepIndex > 0) {
      const previousStep = stepOrder[currentStepIndex - 1];
      dispatch({ type: 'SET_STEP', payload: previousStep });
    }
  };

  const handleStepClick = (step: InvoiceStep) => {
    dispatch({ type: 'SET_STEP', payload: step });
  };

  const handlePreview = () => {
    navigate('/template');
  };

  const renderCurrentStep = () => {
    switch (state.currentStep) {
      case 'details':
        return <DetailsStep />;
      case 'parties':
        return <PartiesStep />;
      case 'shipping':
        return <ShippingStep />;
      case 'items':
        return <ItemsStep />;
      case 'preview':
        return <PreviewStep />;
      default:
        return <DetailsStep />;
    }
  };

  return (
    <div className="space-y-8">
      <StepIndicator
        currentStep={state.currentStep}
        onStepClick={handleStepClick}
        completedSteps={completedSteps}
      />

      {renderCurrentStep()}

      {/* Navigation */}
      <div className="flex items-center justify-between bg-white rounded-lg shadow-sm p-6">
        <button
          onClick={handlePrevious}
          disabled={currentStepIndex === 0}
          className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-all duration-300 ${
            currentStepIndex === 0
              ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
          }`}
        >
          <ChevronLeft className="w-4 h-4" />
          <span>Anterior</span>
        </button>

        <div className="flex space-x-4">
          {state.currentStep === 'preview' && (
            <button
              onClick={handlePreview}
              className="flex items-center space-x-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-all duration-300"
            >
              <Eye className="w-4 h-4" />
              <span>Visualizar Templates</span>
            </button>
          )}

          <button
            onClick={handleNext}
            disabled={!canProceed || isLastStep}
            className={`flex items-center space-x-2 px-6 py-3 rounded-lg transition-all duration-300 ${
              !canProceed || isLastStep
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700'
            }`}
          >
            <span>Próximo</span>
            <ChevronRight className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
}