import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { InvoiceData } from '../types/invoice';

export async function generatePDF(invoiceData: InvoiceData): Promise<void> {
  const element = document.getElementById('invoice-preview');
  if (!element) {
    throw new Error('Elemento de preview não encontrado');
  }

  try {
    // Configure html2canvas options for better quality
    const canvas = await html2canvas(element, {
      scale: 2,
      useCORS: true,
      allowTaint: true,
      backgroundColor: '#ffffff',
      logging: false,
      width: element.scrollWidth,
      height: element.scrollHeight,
    });

    // Create PDF in A4 format
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    // Calculate dimensions for A4
    const pdfWidth = 210; // A4 width in mm
    const pdfHeight = 297; // A4 height in mm
    const imgWidth = pdfWidth;
    const imgHeight = (canvas.height * pdfWidth) / canvas.width;

    // Add image to PDF
    pdf.addImage(
      canvas.toDataURL('image/jpeg', 0.95),
      'JPEG',
      0,
      0,
      imgWidth,
      Math.min(imgHeight, pdfHeight)
    );

    // Add new page if content is too long
    if (imgHeight > pdfHeight) {
      pdf.addPage();
      pdf.addImage(
        canvas.toDataURL('image/jpeg', 0.95),
        'JPEG',
        0,
        -(pdfHeight),
        imgWidth,
        imgHeight
      );
    }

    // Download the PDF
    const fileName = `Fatura_${invoiceData.details.number}_${new Date().toISOString().split('T')[0]}.pdf`;
    pdf.save(fileName);
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw new Error('Erro ao gerar PDF');
  }
}