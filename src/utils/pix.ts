import QRCode from 'qrcode';
import { PixInfo } from '../types/invoice';

export function generatePixPayload(pixInfo: PixInfo): string {
  const merchantAccountInformation = `0014br.gov.bcb.pix01${String(pixInfo.key.length).padStart(2, '0')}${pixInfo.key}`;
  const merchantCategoryCode = '0000';
  const transactionCurrency = '986'; // BRL
  const transactionAmount = String(pixInfo.amount.toFixed(2));
  const countryCode = 'BR';
  const merchantName = pixInfo.name.substring(0, 25).toUpperCase();
  const merchantCity = pixInfo.city.substring(0, 15).toUpperCase();
  
  let payload = '';
  payload += '********'; // Payload Format Indicator
  payload += `26${String(merchantAccountInformation.length).padStart(2, '0')}${merchantAccountInformation}`;
  payload += `52${String(merchantCategoryCode.length).padStart(2, '0')}${merchantCategoryCode}`;
  payload += `53${String(transactionCurrency.length).padStart(2, '0')}${transactionCurrency}`;
  payload += `54${String(transactionAmount.length).padStart(2, '0')}${transactionAmount}`;
  payload += `58${String(countryCode.length).padStart(2, '0')}${countryCode}`;
  payload += `59${String(merchantName.length).padStart(2, '0')}${merchantName}`;
  payload += `60${String(merchantCity.length).padStart(2, '0')}${merchantCity}`;
  payload += '6304'; // CRC16
  
  // Calculate CRC16
  const crc = calculateCRC16(payload);
  payload += crc;
  
  return payload;
}

function calculateCRC16(payload: string): string {
  let crc = 0xFFFF;
  const data = new TextEncoder().encode(payload);
  
  for (let i = 0; i < data.length; i++) {
    crc ^= data[i] << 8;
    for (let j = 0; j < 8; j++) {
      if (crc & 0x8000) {
        crc = (crc << 1) ^ 0x1021;
      } else {
        crc = crc << 1;
      }
    }
  }
  
  crc &= 0xFFFF;
  return crc.toString(16).toUpperCase().padStart(4, '0');
}

export async function generatePixQRCode(pixInfo: PixInfo): Promise<string> {
  const payload = generatePixPayload(pixInfo);
  
  try {
    return await QRCode.toDataURL(payload, {
      errorCorrectionLevel: 'M',
      type: 'image/png',
      quality: 0.92,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF',
      },
    });
  } catch (error) {
    console.error('Error generating QR Code:', error);
    throw new Error('Erro ao gerar QR Code PIX');
  }
}