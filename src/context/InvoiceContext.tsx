import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { InvoiceData, InvoiceStep } from '../types/invoice';
import { generateInvoiceNumber } from '../utils/invoice';

interface InvoiceState {
  currentStep: InvoiceStep;
  data: InvoiceData;
}

type InvoiceAction = 
  | { type: 'SET_STEP'; payload: InvoiceStep }
  | { type: 'UPDATE_DETAILS'; payload: Partial<InvoiceData['details']> }
  | { type: 'UPDATE_COMPANY'; payload: Partial<InvoiceData['company']> }
  | { type: 'UPDATE_CLIENT'; payload: Partial<InvoiceData['client']> }
  | { type: 'UPDATE_SHIPPING'; payload: Partial<InvoiceData['shipping']> }
  | { type: 'UPDATE_ITEMS'; payload: InvoiceData['items'] }
  | { type: 'UPDATE_PIX'; payload: Partial<InvoiceData['pix']> }
  | { type: 'UPDATE_TEMPLATE'; payload: string }
  | { type: 'CALCULATE_TOTALS' }
  | { type: 'LOAD_DATA'; payload: InvoiceData };

const initialData: InvoiceData = {
  details: {
    number: generateInvoiceNumber(),
    issueDate: new Date(),
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
    currency: 'BRL',
    notes: '',
    termsConditions: '',
  },
  company: {
    name: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    phone: '',
    email: '',
    cnpj: '',
    website: '',
  },
  client: {
    name: '',
    address: '',
    city: '',
    state: '',
    zipCode: '',
    phone: '',
    email: '',
    cpfCnpj: '',
  },
  shipping: {},
  items: [],
  pix: {
    key: '',
    name: '',
    city: '',
    amount: 0,
  },
  subtotal: 0,
  taxRate: 0,
  taxAmount: 0,
  total: 0,
  template: 'modern',
};

const initialState: InvoiceState = {
  currentStep: 'details',
  data: initialData,
};

function invoiceReducer(state: InvoiceState, action: InvoiceAction): InvoiceState {
  switch (action.type) {
    case 'SET_STEP':
      return { ...state, currentStep: action.payload };
    
    case 'UPDATE_DETAILS':
      return {
        ...state,
        data: { ...state.data, details: { ...state.data.details, ...action.payload } },
      };
    
    case 'UPDATE_COMPANY':
      return {
        ...state,
        data: { ...state.data, company: { ...state.data.company, ...action.payload } },
      };
    
    case 'UPDATE_CLIENT':
      return {
        ...state,
        data: { ...state.data, client: { ...state.data.client, ...action.payload } },
      };
    
    case 'UPDATE_SHIPPING':
      return {
        ...state,
        data: { ...state.data, shipping: { ...state.data.shipping, ...action.payload } },
      };
    
    case 'UPDATE_ITEMS':
      const newState = {
        ...state,
        data: { ...state.data, items: action.payload },
      };
      return calculateTotals(newState);
    
    case 'UPDATE_PIX':
      return {
        ...state,
        data: { ...state.data, pix: { ...state.data.pix, ...action.payload } },
      };
    
    case 'UPDATE_TEMPLATE':
      return {
        ...state,
        data: { ...state.data, template: action.payload },
      };
    
    case 'CALCULATE_TOTALS':
      return calculateTotals(state);
    
    case 'LOAD_DATA':
      return { ...state, data: action.payload };
    
    default:
      return state;
  }
}

function calculateTotals(state: InvoiceState): InvoiceState {
  const subtotal = state.data.items.reduce((sum, item) => sum + item.total, 0);
  const taxAmount = subtotal * (state.data.taxRate / 100);
  const total = subtotal + taxAmount;
  
  return {
    ...state,
    data: {
      ...state.data,
      subtotal,
      taxAmount,
      total,
      pix: { ...state.data.pix, amount: total },
    },
  };
}

interface InvoiceContextType {
  state: InvoiceState;
  dispatch: React.Dispatch<InvoiceAction>;
}

const InvoiceContext = createContext<InvoiceContextType | undefined>(undefined);

export function InvoiceProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(invoiceReducer, initialState);

  // Auto-save to localStorage
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      localStorage.setItem('billify-invoice-data', JSON.stringify(state.data));
    }, 1000);

    return () => clearTimeout(timeoutId);
  }, [state.data]);

  // Load data from localStorage on mount
  useEffect(() => {
    const savedData = localStorage.getItem('billify-invoice-data');
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData);
        // Convert date strings back to Date objects
        if (parsedData.details) {
          if (parsedData.details.issueDate) {
            parsedData.details.issueDate = new Date(parsedData.details.issueDate);
          }
          if (parsedData.details.dueDate) {
            parsedData.details.dueDate = new Date(parsedData.details.dueDate);
          }
        }
        dispatch({ type: 'LOAD_DATA', payload: parsedData });
      } catch (error) {
        console.error('Error loading saved data:', error);
      }
    }
  }, []);

  return (
    <InvoiceContext.Provider value={{ state, dispatch }}>
      {children}
    </InvoiceContext.Provider>
  );
}

export function useInvoice() {
  const context = useContext(InvoiceContext);
  if (context === undefined) {
    throw new Error('useInvoice must be used within an InvoiceProvider');
  }
  return context;
}